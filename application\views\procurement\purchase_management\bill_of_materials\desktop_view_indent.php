<ul class="breadcrumb" style="margin-bottom: 20px;">
    <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2'); ?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/inventory_controller_v2/indent_widgets_v2'); ?>">Manage Indents</a>
    </li>
    <li>Indent Details</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="panel-header"
                style="margin: 0; background: none; border-bottom: 1px solid lightgray; height: 3.7rem; display: flex; align-items: center;">
                <h3 style="margin: 0; font-size: 1.5rem;">
                    <a class="back_anchor"
                        href="<?php echo site_url('procurement/requisition_controller_v2/indents') ?>"
                        class="control-primary">
                        <span class="fa fa-arrow-left"></span>
                    </a>
                    Indent Details - <?php
                    echo (strlen($indentName) > 50)
                        ? substr($indentName, 0, 47) . '...'
                        : $indentName;
                    ?> <span class="badge" style="color: ;"><?php echo $indentStatus ?></span>
                </h3>
            </div>
        </div>
        <div class="col-md-12 container" style="margin-bottom: 20px;">
            <!-- content starts -->
            <div class="content" style="overflow: auto; max-height: 83vh; padding: 10px;">
                <ul class="nav nav-tabs" id="myTab" role="tablist" style="margin-bottom: 20px;">
                    <li class="nav-item" data-indent-id="<?php echo $indentId; ?>" style="margin-top: 3rem;"
                        role="presentation">
                        <button class="nav-link active" style="min-width: 10rem;" id="bom-details-tab"
                            data-bs-toggle="tab" data-bs-target="#bom-details" type="button" role="tab"
                            aria-controls="bom-details" aria-selected="true" data-tab="indent-details">Indent
                            Details</button>
                    </li>
                    <li class="nav-item" data-indent-id="<?php echo $indentId; ?>" style="margin-top: 3rem;"
                        role="presentation">
                        <button class="nav-link" id="bom-approval-tab" style="min-width: 10rem;" data-bs-toggle="tab"
                            data-bs-target="#bom-approval" type="button" role="tab" aria-controls="bom-approval"
                            aria-selected="false" onclick="showIndentApprovalTable('<?php echo $indentId; ?>')"
                            data-tab="indent-approval">Indent
                            Approval</button>
                    </li>
                    <li class="nav-item" data-indent-id="<?php echo $indentId; ?>" style="margin-top: 3rem;"
                        role="presentation">
                        <button class="nav-link" id="quotation-files-tab" style="min-width: 10rem;" data-bs-toggle="tab"
                            data-bs-target="#quotation-files" type="button" role="tab" aria-controls="quotation-files"
                            aria-selected="false" onclick="showQuotationFiles('<?php echo $indentId; ?>')"
                            data-tab="rfqs">RFQs</button>
                    </li>
                    <li class="nav-item" data-indent-id="<?php echo $indentId; ?>" style="margin-top: 3rem;"
                        role="presentation">
                        <button class="nav-link" id="quotation-approval-tab" style="min-width: 10rem;"
                            data-bs-toggle="tab" data-bs-target="#quotation-approval" type="button" role="tab"
                            aria-controls="quotation-approval" aria-selected="false"
                            onclick="showQuotationApprovalTable('<?php echo $indentId; ?>',2)"
                            data-tab="rfq-approval">RFQ Approval</button>
                    </li>
                    <li class="nav-item" data-indent-id="<?php echo $indentId; ?>" style="margin-top: 3rem;"
                        role="presentation">
                        <button class="nav-link" id="history-tab" style="min-width: 10rem;" data-bs-toggle="tab"
                            data-bs-target="#history" type="button" role="tab" aria-controls="history"
                            aria-selected="false" onclick="showIndentHistoryTable('<?php echo $indentId; ?>')"
                            data-tab="rfq-history">History</button>
                    </li>
                </ul>
                <div class="tab-content" id="myTabContent">
                    <div class="tab-pane fade show active" id="bom-details" role="tabpanel"
                        aria-labelledby="bom-details-tab" style="overflow: auto; max-height: 400px; padding: 10px;">
                        <div id="show-bom-details" style="overflow: auto; max-height: 400px; width: 100%;">
                            <div class="summary-content"
                                style="padding: 15px; background-color: #f9f9f9; border: 1px solid #ddd; border-radius: 5px;">
                                <h4 style="margin-bottom: 10px;">Details</h4>
                                <p style="margin: 0; font-size: 14px; color: #555;">Loading...</p>
                            </div>
                            <div class="text-center loader" style="margin-top: 20px;">
                                <div class="spinner-border text-secondary" role="status">
                                    <span class="sr-only">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="bom-approval" role="tabpanel" aria-labelledby="bom-approval-tab">
                        <div style="padding: 10px;">
                            <div id="show-bom-approval" style="overflow: auto; max-height: 400px; width: 100%;">
                                <div class="text-center loader">
                                    <div class="spinner-border text-secondary" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="quotation-files" role="tabpanel"
                        aria-labelledby="quotation-files-tab">
                        <div style="padding: 10px;">
                            <h5 id="original-indent-price"></h5>
                            <div id="show-quotation-files" style="overflow: auto; max-height: 400px; width: 100%;">
                                <div class="text-center loader">
                                    <div class="spinner-border text-secondary" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="quotation-approval" role="tabpanel"
                        aria-labelledby="quotation-approval-tab">
                        <div style="padding: 10px;">
                            <div id="show-quotation-approval" style="overflow: auto; max-height: 400px; width: 100%;">
                                <div class="text-center loader">
                                    <div class="spinner-border text-secondary" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="history" role="tabpanel" aria-labelledby="history-tab">
                        <div style="padding: 10px;">
                            <div id="show-history" style="overflow: auto; max-height: 400px; width: 100%;">
                                <div class="text-center loader">
                                    <div class="spinner-border text-secondary" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- content ends -->
        </div>
    </div>
</div>

<script type="text/javascript" src="<?php echo base_url(); ?>assets/js/plugins/summernote/summernote.js"></script>
<?php $this->load->view("procurement/purchase_management/bill_of_materials/script.php") ?>
<?php $this->load->view("procurement/purchase_management/bill_of_materials/upload_file_modalv2.php") ?>
<?php $this->load->view("procurement/purchase_management/bill_of_materials/show_quotation_file_modal.php") ?>
<?php $this->load->view("procurement/purchase_management/bill_of_materials/show_add_items_modal.php") ?>