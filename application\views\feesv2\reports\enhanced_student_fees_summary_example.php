<!DOCTYPE html>
<html>
<head>
    <title>Enhanced Student Fees Summary - Regular + Component Data</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .component-breakdown { 
            background-color: #f8f9fa; 
            margin: 5px 0; 
            padding: 5px; 
            border-left: 3px solid #007bff; 
        }
        .regular-data { 
            background-color: #fff3cd; 
            padding: 10px; 
            margin: 5px 0; 
        }
        .toggle-btn { 
            background-color: #28a745; 
            color: white; 
            border: none; 
            padding: 5px 10px; 
            cursor: pointer; 
        }
    </style>
</head>
<body>

<div class="container">
    <h2>Enhanced Student Fees Summary Report</h2>
    
    <!-- Filter Controls -->
    <div class="filters">
        <select id="fee_type">
            <option value="">Select Fee Type</option>
            <!-- Fee types will be populated here -->
        </select>
        
        <select id="class_id">
            <option value="">Select Class</option>
            <!-- Classes will be populated here -->
        </select>
        
        <label>
            <input type="checkbox" id="include_components" value="yes"> Include Component Breakdown
        </label>
        
        <button onclick="getEnhancedReport()">Get Enhanced Report</button>
    </div>
    
    <!-- Results Display -->
    <div id="results"></div>
</div>

<script>
/**
 * Enhanced function to get both regular and component-wise data
 */
function getEnhancedReport() {
    // Sample student IDs - in real implementation, get from filters
    var cohortstudentids = [1, 2, 3, 4, 5]; // Replace with actual student IDs
    
    var include_components = $('#include_components').is(':checked') ? 'yes' : 'no';
    var fee_type = $('#fee_type').val();
    var clsId = $('#class_id').val();
    
    $.ajax({
        url: '<?php echo site_url('feesv2/reports_v2/getStudentsForSummary_v2_details_enhanced'); ?>',
        type: 'post',
        data: {
            'cohortstudentids': cohortstudentids,
            'fee_type': fee_type,
            'clsId': clsId,
            'include_components': include_components,
            'payment_status': '',
            'admission_type': '',
            'mediumId': '',
            'category': '',
            'donorsId': '',
            'created_byId': '',
            'classSectionId': '',
            'rte_nrteId': '',
            'acad_year_id': '',
            'combination': '',
            'installmentId': '',
            'transaction_hide_show': 0
        },
        success: function(data) {
            var result = JSON.parse(data);
            displayEnhancedResults(result);
        },
        error: function() {
            $('#results').html('<div class="alert alert-danger">Error occurred while fetching data.</div>');
        }
    });
}

/**
 * Display both regular and component data
 */
function displayEnhancedResults(data) {
    var output = '<div class="enhanced-results">';
    
    // Check if component data is available
    if (data.has_component_data) {
        output += '<h3>Enhanced Report (Regular + Component Data)</h3>';
        output += '<p><strong>Component Headers Available:</strong> ' + Object.keys(data.component_headers || {}).length + '</p>';
    } else {
        output += '<h3>Regular Report Only</h3>';
    }
    
    // Display regular fee data
    output += '<div class="regular-data">';
    output += '<h4>Regular Fee Summary</h4>';
    output += '<table class="table table-bordered">';
    output += '<thead><tr>';
    output += '<th>Student</th><th>Class</th><th>Admission No</th><th>Total Fee</th><th>Paid</th><th>Balance</th>';
    if (data.has_component_data) {
        output += '<th>Component Details</th>';
    }
    output += '</tr></thead><tbody>';
    
    // Loop through fee data
    if (data.fee_data && data.fee_data.length > 0) {
        for (var i = 0; i < data.fee_data.length; i++) {
            var student = data.fee_data[i];
            output += '<tr>';
            output += '<td>' + (student.student_name || 'N/A') + '</td>';
            output += '<td>' + (student.class_name || 'N/A') + '</td>';
            output += '<td>' + (student.admission_no || 'N/A') + '</td>';
            output += '<td>' + (student.total_fee || 0) + '</td>';
            output += '<td>' + (student.total_fee_paid || 0) + '</td>';
            output += '<td>' + (student.total_balance || 0) + '</td>';
            
            // Add component breakdown if available
            if (data.has_component_data && student.component_breakdown) {
                output += '<td>';
                output += '<button class="toggle-btn" onclick="toggleComponentBreakdown(' + student.stdId + ')">View Components</button>';
                output += '<div id="components_' + student.stdId + '" class="component-breakdown" style="display:none;">';
                
                for (var componentKey in student.component_breakdown) {
                    var component = student.component_breakdown[componentKey];
                    output += '<div>';
                    output += '<strong>' + componentKey + ':</strong><br>';
                    output += 'Amount: ' + (component.component_amount || 0) + '<br>';
                    output += 'Paid: ' + (component.component_amount_paid || 0) + '<br>';
                    output += 'Balance: ' + (component.balance_amount || 0) + '<br>';
                    output += '</div><hr>';
                }
                
                output += '</div>';
                output += '</td>';
            } else if (data.has_component_data) {
                output += '<td>No component data</td>';
            }
            
            output += '</tr>';
        }
    } else {
        output += '<tr><td colspan="' + (data.has_component_data ? '7' : '6') + '">No data found</td></tr>';
    }
    
    output += '</tbody></table>';
    output += '</div>';
    
    // Display component headers if available
    if (data.component_headers) {
        output += '<div class="component-headers">';
        output += '<h4>Available Components</h4>';
        output += '<ul>';
        for (var header in data.component_headers) {
            output += '<li>' + data.component_headers[header] + '</li>';
        }
        output += '</ul>';
        output += '</div>';
    }
    
    output += '</div>';
    
    $('#results').html(output);
}

/**
 * Toggle component breakdown visibility
 */
function toggleComponentBreakdown(studentId) {
    var componentDiv = $('#components_' + studentId);
    if (componentDiv.is(':visible')) {
        componentDiv.hide();
    } else {
        componentDiv.show();
    }
}

/**
 * Example of using the regular function (backward compatibility)
 */
function getRegularReport() {
    var cohortstudentids = [1, 2, 3, 4, 5];
    
    $.ajax({
        url: '<?php echo site_url('feesv2/reports_v2/getStudentsForSummary_v2_details_new'); ?>',
        type: 'post',
        data: {
            'cohortstudentids': cohortstudentids,
            'fee_type': $('#fee_type').val(),
            'clsId': $('#class_id').val(),
            // ... other regular parameters
        },
        success: function(data) {
            var result = JSON.parse(data);
            // Handle regular data only
            console.log('Regular data:', result);
        }
    });
}

/**
 * Example of getting component data separately
 */
function getComponentDataSeparately() {
    var cohortstudentids = [1, 2, 3, 4, 5];
    
    $.ajax({
        url: '<?php echo site_url('feesv2/reports_v2/get_student_wise_component_getStudentsForSummary'); ?>',
        type: 'post',
        data: {
            'cohortstudentids': cohortstudentids,
            'fee_type': $('#fee_type').val(),
            'clsId': $('#class_id').val(),
            // ... other parameters
        },
        success: function(data) {
            var result = JSON.parse(data);
            console.log('Component data:', result);
        }
    });
}
</script>

</body>
</html>
