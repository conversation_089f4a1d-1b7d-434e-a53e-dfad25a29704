<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('management/payroll/'); ?>">Payroll dashboard</a></li>
    <li>Mass Salary Structure Update</li>
</ul>

<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-8 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('management/payroll/'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Export Excel to Salary Structure
                    </h3>
                </div>
                <div class="col-md-4">
                    <a href="<?php echo site_url('management/payroll/downloadCsvFormat_salary_structure');?>" class="btn btn-primary" style="float:right;">Download CSV Format</a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <form enctype="multipart/form-data" method="post" id="sub_csv_event_form" data-parsley-validate="" class="form-horizontal col-md-12">
                <div class="row mb-3 align-items-center">
                    <!-- Label -->
                    <div class="col-auto">
                        <label class="form-label mb-0">Upload CSV</label>
                    </div>

                    <!-- File input -->
                    <div class="col">
                        <input type="file" name="payroll_data" accept=".csv" onchange="enableGetData()" class="form-control">
                    </div>

                    <!-- Button -->
                    <div class="col-auto">
                        <button type="button" id="salary_upload_btn" class="btn btn-primary" onclick="upload_csv_salary()" disabled>
                            Get Data
                        </button>
                    </div>
                </div>
            </form>
            <div class="card-body">
                <div class="col-md-12 px-0" id="display_content_csv_file">
                </div>
                <center>
                    <button type="button" style="display:none" id="sub_event_submit" style="width: 9rem; border-radius: .45rem;" class="btn btn-primary" onclick="submit_csv_salary_data()">Submit</button>
                </center>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript">
function enableGetData(){
    staffIds = [];
    let value = $('input[name="payroll_data"]').val();
    if(!value){
        $('#salary_upload_btn').prop('disabled', true);
        return;
    }
    $('#salary_upload_btn').prop('disabled', false);
    $('#salary_upload_btn').html('Get Data');
    $('#display_content_csv_file').html('');
    $('#sub_event_submit').hide();
}
function upload_csv_salary() {
    $('#sub_event_submit').hide();
    var upload_csv = $('input[name="payroll_data"]').prop('files')[0];
    if(!upload_csv){
        Swal.fire({
            icon: 'error',
            title: 'Oops...',
            text: 'Please select a csv file to upload!',
        });
        return;
    }
    var fileName = upload_csv.name;
    var fileExtension = fileName.split('.').pop().toLowerCase();

    if (fileExtension !== 'csv') {
        Swal.fire({
            icon: 'error',
            title: 'Oops...',
            text: 'Please select a valid csv file to upload!',
        });
        $('input[name="payroll_data"]').val('');
        return;
    }
    var $form = $('#sub_csv_event_form');
    if ($form.parsley().validate()) {
        var form = $('#sub_csv_event_form')[0];
        var formData = new FormData(form);
        formData.append('file[]', upload_csv);
        $('input[name="payroll_data"]').prop('disabled', true);
        $('#salary_upload_btn').prop('disabled', true);
        $('#salary_upload_btn').html('Please Wait...');
        $('#display_content_csv_file').html('<div class="no-data-display mb-3"><i class="fa fa-spin fa-spinner"></i> Loading...</div>');
        $.ajax({
            url: '<?php echo site_url('management/payroll/upload_csvfor_salary_structure'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(resData) {
                var csvdetails = $.parseJSON(resData);
                // console.log(csvdetails);
                if (csvdetails.length > 0) {
                    $('#sub_event_submit').show();
                    $('#sub_event_submit').prop('disabled', false);
                    $('#salary_upload_btn').attr('disabled', 'disabled');
                    $('#display_content_csv_file').html(construct_csv_sub_event_table(csvdetails));
                    $('#reportTable').DataTable({
                        destroy: true,
                        ordering: false,
                        paging: false,
                        searching: true,
                        scrollX: true,
                        scrollY: 300,
                        scrollCollapse: true,
                        dom: 'lrtip',
                        language: {
                            search: "",
                        },
                        buttons: []
                    });
                } else {
                    $('display_content_csv_file').html('File Not Support.');
                }
                $('input[name="payroll_data"]').prop('disabled', false);
                // $('#salary_upload_btn').prop('disabled', false);
                $('#salary_upload_btn').html('Get Data');
            },
            error: function(err) {
                console.log(err);
                $('#display_content_csv_file').html('Something went wrong. Please try again.');
                $('#sub_event_submit').hide();
                $('input[name="payroll_data"]').prop('disabled', false);
                $('#salary_upload_btn').prop('disabled', false);
                $('#salary_upload_btn').html('Get Data');
            }
        });
    }
}
let csvdata_obj = {};
let staffIds = [];

function construct_csv_sub_event_table(csvdata) {
    let html1 = ``;
    html1 += '<div class="col-md-12 px-0"><p class="sub_header_note"><strong></strong>Note:</strong> Please <span style="font-weight:600;font-size:15px;color:blue">ensure that a slab is assigned to each staff member</span> before clicking the Submit button.</p></div>';
    html1 += '<table id="reportTable" class="table table-bordered">';
    html1 += '<thead>';
    html1 += '<tr>';
    html1 += '<th>#</th>';
    html1 += '<th>Action</th>';
    html1 += '<th>employee_code</th>';
    html1 += '<th style="min-width: 150px;">staff_name</th>';
    html1 += `<th style="white-space: nowrap !important;">
                <select class="form-control custom-select" onchange="mass_select_slab_id()" id="mass_updater_slab_id" style="width: 100px !important;">
                    <option value="">Select Slab</option>`;
                        <?php foreach ($slabs as $slab) { ?>
                            html1 += `<option value="<?php echo $slab->id ?>"><?php echo $slab->slab_name ?></option>`;
                        <?php } ?>
        html1 += `</select>
        </th>`;
    for (var key in csvdata[0]) {
        if (csvdata[0].hasOwnProperty(key) && key !== 'staff_id' && key !== 'employee_code' && key !== 'staff_name') {
            html1 += '<th>' + key + '</th>';
        }
    }
    html1 += '</tr>';
    html1 += '</thead>';
    html1 += '<tbody>';
    for (var i = 0; i < csvdata.length; i++) {
        var staffId = csvdata[i]['staff_id'];
        staffIds.push(staffId);
        html1 += '<tr id="row_staff_id_' + staffId + '" >';
        html1 += '<td>' + (i + 1) + '</td>';
        // html1 += `<td id="action_csvfiled_${staffId}" class="text-center;">
        //             <i class="fa fa-times text-danger removeStaffButtonInTable" onclick="remove_csvfiled(this)" data-toggle="tooltip" data-placement="left" data-original-title="Remove" style="font-size: 1.5rem; cursor: pointer;"></i>
        //         </td>`;
        html1 += `<td class="text-center">
                        <button type="button"
                                class="btn p-0 removeStaffButtonInTable"
                                onclick="remove_csvfiled(this)"
                                data-toggle="tooltip"
                                data-placement="left"
                                data-original-title="Remove">
                            <i class="fa fa-times text-danger" style="font-size: 1.5rem;"></i>
                        </button>
                    </td>`;
        html1 += '<td>' + csvdata[i]['employee_code'] + '</td>';
        html1 += '<td style="min-width: 150px;">' + csvdata[i]['staff_name'] + '</td>';
        html1 += `<td style="white-space: nowrap !important;">
                    <select class="form-control custom-select select_slabs_class" required="" id="slab_id_${staffId}" name="slab[${staffId}]" style="width: 100px !important;">`;
                        html1 += '<option value="">Select Slab</option>';
                        <?php foreach ($slabs as $slab) { ?>
                            html1 += '<option value="<?php echo $slab->id ?>"><?php echo $slab->slab_name ?></option>';
                        <?php } ?>
            html1 += `</select>
                </td>`;
        for (var key in csvdata[i]) {

            if (csvdata[i].hasOwnProperty(key) && key !== 'staff_id' && key !== 'employee_code' && key !== 'staff_name') {
                html1 += '<td>' + csvdata[i][key] + '</td>';
                var namekey = key;
                if (key == 'monthly_ctc') {
                    namekey = 'monthly_gross';
                }
                html1 += '<input type="hidden" class="staff_columns_data' + staffId +
                    '" name="structure_column[' + namekey + ']" id="staff_structure_' + staffId +
                    '" value="' + csvdata[i][key] + '">';
            }
        }
        html1 += '</tr>';
    }
    html1 += '</tbody>';
    html1 += '</table>';
    return html1;
}

function remove_csvfiled(ele) {
    var row = $(ele).closest('tr');
    var staffId = row.attr('id').replace('row_staff_id_', '');
    staffIds = staffIds.filter(id => id !== staffId); // Remove staffId from staffIds array
    row.remove();

    // Update row numbers
    var rows = $('#reportTable tbody tr');
    rows.each(function(index) {
        $(this).find('td:first-child').text(index + 1);
    });
}

function mass_select_slab_id() {
    var slabId = $('#mass_updater_slab_id').val();
    $('.select_slabs_class').val(slabId);
}

async function submit_csv_salary_data() {
    $('#sub_event_submit').attr('disabled', 'disabled').html('Please Wait...');
    $('.removeStaffButtonInTable').prop('disabled', true);
    $('select').prop('disabled', true);
    for (var i = 0; i < staffIds.length; i++) {
        try {
            const result = await salaray_data_one_by_one(staffIds[i]);
            if (i == staffIds.length - 1) {
                Swal.fire({
                    icon: "success",
                    title: "Salary Mass Upload Process Completed!",
                    showConfirmButton: false,
                    timer: 1500
                }).then(function() {
                    $('#sub_event_submit').attr('disabled', 'disabled').html('Submit');
                });
            }
        } catch (error) {
            console.error(error);
        }
    }

}

function salaray_data_one_by_one(staff_id) {
    return new Promise((resolve, reject) => {
        var slabId = $('#slab_id_' + staff_id).val();
        var salaryColumns = {};
        $('.staff_columns_data' + staff_id).each(function() {
            var columnName = $(this).attr('name').replace(/^.*\[(.*)\]$/, '$1');
            var columnValue = $(this).val();
            salaryColumns[columnName] = columnValue;
        });
        let row = $('#row_staff_id_' + staff_id);
        let secondTd = row.find('td:eq(1)');
        secondTd.html('<i class="fa fa-spinner fa-spin text-primary" data-toggle="tooltip" data-placement="left" data-original-title="Processing" style="font-size: 1.5rem;"></i>');
        setTimeout(() => {
            $.ajax({
                url: '<?php echo site_url('management/payroll/submit_csv_salary_data_staff_wise'); ?>',
                type: 'post',
                data: {
                    staff_id: staff_id,
                    slab_id: slabId,
                    salary_columns: salaryColumns
                },
                success: function(data) {
                    var resData = data.trim();
                    if (resData == 1) {
                        row.addClass('success-row');
                        secondTd.html('<i class="fa fa-info-circle text-success" data-toggle="tooltip" data-placement="left" data-original-title="Data Uploaded" style="font-size: 1.5rem;"></i>');
                    } else if(resData == -1) {
                        row.addClass('failed-row');
                        secondTd.html('<i class="fa fa-info-circle text-danger" data-toggle="tooltip" data-placement="left" data-original-title="Consultant Staff - Cannot Upload" style="font-size: 1.5rem;"></i>');
                    } else if(resData == -2) {
                        row.addClass('failed-row');
                        secondTd.html('<i class="fa fa-info-circle text-danger" data-toggle="tooltip" data-placement="left" data-original-title="Slab Not Selected" style="font-size: 1.5rem;"></i>');
                    } else if(resData == -3) {
                        row.addClass('failed-row');
                        secondTd.html('<i class="fa fa-info-circle text-danger" data-toggle="tooltip" data-placement="left" data-original-title="Monthly Gross Is Zero" style="font-size: 1.5rem;"></i>');
                    } else {
                        row.addClass('failed-row');
                        secondTd.html('<i class="fa fa-info-circle text-danger" data-toggle="tooltip" data-placement="left" data-original-title="Something Went Wrong!" style="font-size: 1.5rem;"></i>');
                    }
                    resolve();
                },
                error: function(err) {
                    console.log(err);
                    row.addClass('failed-row');
                    secondTd.html('<i class="fa fa-info-circle text-danger" data-toggle="tooltip" data-original-title="Something Went Wrong!"></i>');
                    reject(err);
                }
            });
        }, 250);
    });
}
</script>

<style type="text/css">
.loaderclass {
    border: 8px solid #eee;
    border-top: 8px solid #7193be;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    position: fixed;
    z-index: 1;
    animation: spin 2s linear infinite;
    margin-top: 20%;
    margin-left: 40%;
    position: absolute;
    z-index: 99999;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.active {
    background: #6893ca;
}

.list-group-item.active {
    background-color: #ebf3f9;
    border-color: #ebf3f9;
    color: #737373;
}

.list-group-item.active,
.list-group-item.active:hover,
.list-group-item.active:focus {
    background: #ebf3f9;
    color: #737373;
}

.list-group-item {
    border: none;
}

.widthadjust {
    width: 48%;
    margin: auto;
}

.widthadjust-alert {
    width: 30%;
    margin: auto;
    /* Adjust the width as needed */
}

.scroller {

    scrollbar-width: thin;
}

.dataTables_wrapper .dt-buttons {
    float: right;
}

.dataTables_filter input {
    background-color: #f2f2f2;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-right: 5px;
}

.dataTables_wrapper .dataTables_filter {
    float: right;
    text-align: left;
    width: unset;
}

#DataTables_Table_0_length {
    text-align: left;
}

.dataTables_scrollBody::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #F5F5F5;
    border-radius: 0px;
}

.dataTables_scrollBody::-webkit-scrollbar {
    width: 10px;
    background-color: #F5F5F5;
    height: 8px;
}

.dataTables_scrollBody::-webkit-scrollbar-thumb {
    background-color: #c6c6c7;
    border-radius: 5px;
}

.dataTables_scrollBody {
    margin-top: -13px;
}

.success-row {
    background-color: #dff0d8;
}

.failed-row {
    background-color: #f2dede;
}

.sub_header_note {
    color: #8f7f7f !important;
    font-size: 14px;
}
</style>