<ul class="breadcrumb">
  <li><a href="<?php echo site_url('avatars');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fee Dashboard</a></li>
  <li>Fee Detail Report</li>
</ul>
<?php
  function requireFilter ($filterArray, $filter) {
    foreach ($filterArray as $f) {
      if ($f == $filter)
        return 1;
    }
    return 0;
  }
?>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-8">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard');?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
           Fee Detail Report
          </h3>
        </div>

        <label class="checkbox-inline"><input style="width:20px;height: 20px;" type="checkbox" name="installment_search" id="installment_search"><span style="font-size:16px; margin-left: 10px;">Show Installments</span></label>

        <label class="checkbox-inline"><input style="width:20px;height: 20px;" type="checkbox"  checked name="transaction_hide_show" id="transaction_hide_show"><span style="font-size:16px; margin-left: 10px;">Hide Transactions</span></label>

        <label class="checkbox-inline"><input style="width:20px;height: 20px;" type="checkbox" name="show_component_wise" id="show_component_wise"><span style="font-size:16px; margin-left: 10px;">Show Component Wise</span></label>

      </div>
    </div>
<style type="text/css">
  p{
    margin-bottom: .5rem;
  }
  input[type=checkbox]{
    margin: 0px 4px;
  }
</style>
  <div class="card-body">
    <div class="col-md-12">
      <div class="row" style="margin: 0px">

      <div class="col-md-3 form-group" id="multiBlueprintSelect">
        <p>Select Fee Type</p>
        <select class="form-control multiselect select" multiple title='All' id="fee_type" name="fee_type">
          <?php foreach ($fee_blueprints as $key => $val) { ?>
            <option <?php if($fee_type == $val->id) echo 'selected' ?> value="<?= $val->id ?>"><?php echo $val->name ?></option>
          <?php } ?>
        </select>
      </div>

      <div class="col-md-2 form-group" style="display: none;" id="blueprintSelect">
        <p>Select Blueprint</p>
        <select class="form-control changeFeeType" id="fee_type" name="fee_type">
          <option value="">All</option>
          <?php foreach ($fee_blueprints as $key => $val) { ?>
            <option value="<?= $val->id ?>"><?php echo $val->name?></option>
          <?php } ?>
          <!-- <option value="sales">Sales</option>
          <option value="application">Applications</option> -->
        </select>
      </div>

      <div class="col-md-2 form-group" id="installmentType" style="display: none;">
        <p>Select Installments Type</p>
        <select class="form-control" name="installment_type" id="installment_type"></select>
      </div>

      <div class="col-md-2 form-group" id="installment" style="display: none;">
        <p>Select Installments</p>
        <select class="form-control onchange_installments" multiple title="All" name="installment" id="installmentId"></select>
      </div>

      <script>
        function toggleInstallmentMode(isInstallmentMode) {
          if (isInstallmentMode) {
            // Hide normal filter
            $('#multiBlueprintSelect').hide();

            // Show new installment-related filters
            $('#blueprintSelect').show();
            $('#installmentType').show();
            $('#installment').show();
          } else {
            // Show normal filter
            $('#multiBlueprintSelect').show();

            // Hide installment-related filters
            $('#blueprintSelect').hide();
            $('#installmentType').hide();
            $('#installment').hide();
          }
        }
      </script>


        <div class="col-md-3 form-group">
          <p>Class</p>
          <?php 
              $array = array();
              // $array[0] = 'Select Classes';
              foreach ($classes as $key => $class) {
                $array[$class->classId] = $class->className; 
              }
          echo form_dropdown("class_name[]", $array, set_value("class_name", $clsId), "id='classId' multiple title='All' class='form-control classId select '");
          ?>
        </div>


        <div class="col-md-3 form-group">
          <p>Class/Section</p>
          <?php 
            $array = array();
            // $array[0] = 'Select Section';
            foreach ($classSectionList as $key => $cl) {
                $array[$cl->id] = $cl->class_name . $cl->section_name;
            }
            echo form_dropdown("classSectionId", $array, '', "id='classSectionId' multiple title='All' class='form-control select'");
          ?>
        </div>
        <?php if ($this->settings->getSetting('fees_stuent_summary_report_show_combination_filter')) { ?>
        <div class="col-md-3 form-group">
          <p>Combination</p>
          <?php 
            $array = array();
            $array[0] = 'Select Combination';
            foreach ($combination as $key => $cl) {
                $array[$cl->combination] = strtoupper($cl->combination);
            }
            echo form_dropdown("combination", $array, '', "id='combination' multiple title='Select Combination' class='form-control select'");
          ?>
        </div>
        <?php } ?>
        <div class="col-md-3 form-group">
          <p>Admission Type</p>
          <?php 
            $array = array();
            $array[0] = 'Select Admission Type';
            foreach ($admission_type as $key => $admission) {
                $array[$key] = ucfirst($admission);
            }
            echo form_dropdown("admission_type", $array, set_value("admission_type"), "id='admission_type' class='form-control'");
          ?>
        </div>


        <div class="col-md-3 form-group">
          <p>Payment Options</p>
          <select  name="payment_status"  id='payment_status' class='form-control'>
            <option value="">Payment Option</option>
            <option value="FULL">Full Payment</option>
            <option value="PARTIAL">Partial Payment</option>
            <option value="NOT_STARTED">Balance</option>
          </select>
        </div>
        <?php if(!empty($rteType)){ ?>
          <div class="col-md-3 form-group">
            <p>IS RTE</p>
            <?php
              $rte_nrte = array();
              $rte_nrte[0] = 'All';
              foreach ($rteType as $key => $rn) {
                  $rte_nrte[$key] = $rn;
              }
              echo form_dropdown("rte_nrte", $rte_nrte, set_value("rte_nrte"), "id='rte_nrteId' class='form-control'");
            ?>
          </div>
        <?php } ?>    
       
       

        <div class="col-md-3 form-group">
          <p>Joining Academic Year</p>
          <?php 
            $AcadArray = array();
            $AcadArray[0] = 'Joining Academic Year';
            foreach ($this->acad_year->getAllYearData() as $yearId => $year) {
                $AcadArray[$year->id] = $year->acad_year;
            }

            echo form_dropdown("acad_year_id", $AcadArray, set_value("acad_year_id"), "id='acad_year_id' class='form-control'");
          ?> 
        </div>
        <?php if ($this->settings->getSetting('fee_report_category_filter')) : ?>
        <div class="col-md-3 form-group">
        <p>Select Category</p>
          <?php 
            $array = array();
            $array[0] = 'Select Category';
            foreach ($category as $key => $cat) {
              $array[$key] = $cat; 
            }
          echo form_dropdown("category", $array, set_value("category"), "id='category' class='form-control'");
          ?>
        </div>
        <?php endif ?>
        <?php if ($this->settings->getSetting('fee_report_donor_filter')) : ?>
        <div class="col-md-3 form-group">
          <p>Select Donors</p>
            <?php 
            $array = array();
            $array[0] = 'Select Donors';
            foreach ($donors as $key => $donor) {
                $array['Null'] = 'Non Donors';
                $array[$donor->donor] = ucfirst($donor->donor);
            }
            echo form_dropdown("donors", $array, set_value("donors"), "id='donorsId' multiple title='All' class='form-control select'");
            ?>
        </div>
        <?php endif ?>
        <div class="col-md-3 form-group">
            <p for="sectionId">Admission Status<font color="red">*</font></p>
            <select id="admission_status" name="admission_status" required class="form-control input-md">
              <option value=""><?php echo "All" ?></option>
              <?php foreach ($admission_status as $value => $type) { ?>
                <option  value="<?php echo $value ?>"><?php echo $type ?></option>
              <?php } ?>
            </select>
        </div>

         <div class="col-md-3 form-group">
            <p>Select Staff Kids</p>
            <select class="form-control" name="staff_kids" id="staff_kid">
              <option value="all">All</option>
              <option value="0">Exclude Staff Kids</option>
              <option value="1">Staff Kids</option>
            </select>
        </div>

        <?php if($this->authorization->isAuthorized('FEESV2.STUDENT_WISE_PREDEFINED_FILTERS')) { ?>
          <div class="col-md-4 report-controls-wrapper">
          <div class="form-group">
            <label class="control-label" style="margin-bottom: 6px;">Saved Reports</label>
            <div class="report-row">
              <select name="" id="filter_types" class="form-control grow-select" onchange="selectFilters()">
                <option value="">Select Report</option>
              </select>

              <?php if($this->authorization->isAuthorized('FEESV2.STUDENT_WISE_PREDEFINED_FILTERS')) { ?>
                <div class="dc-buttons">
                  <input type="button" name="reload" id="reload_filter" class="btn btn-info" value="Reload" onclick="selectFilters()">
                  <input type="button" name="save" id="save_filter" class="btn btn-info" value="Save">
                  <input type="button" name="update" id="update_filter" class="btn btn-info" value="Update">
                </div>
            </div>
          </div>


                <style>
                  .report-row {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                  }

                  .grow-select {
                    flex: 1.3; 
                    min-width: 200px;
                  }

                  .dc-buttons {
                    display: flex;
                    gap: 10px;
                    flex-shrink: 0;
                  }

                  @media (max-width: 1400px) {
                    .report-row {
                      flex-direction: column;
                      align-items: flex-start;
                    }

                    .grow-select {
                      width: 100%;
                      margin-bottom: 5px;
                    }

                    .dt-buttons {
                      width: 100%;
                      justify-content: flex-start;
                      margin-top: 5px;
                    }

                    .dt-buttons input.btn {
                      flex: 1;
                      text-align: center;
                    }
                  }
                </style>

         <script>
                        $(document).ready(function(){
                        // get_report();
                        get_predefined_filters();

                          $('#save_filter').on('click', function() {
                              saveFilter();
                          });

                          $('#update_filter').on('click', function() {
                              updateFilter();
                          });

                          $('.select').on('keydown', function(e) {
                              if (e.key === 'Escape') {
                                  $(this).closest('.dropdown').find('.dropdown-toggle').dropdown('hide');
                              }
                              
                              if (e.key === 'Enter') {
                                  $(this).closest('.dropdown').find('.dropdown-toggle').dropdown('hide');
                              }
                              
                              if (e.key === ' ' && !$(this).is(':focus')) {
                                  e.preventDefault();
                              }
                          });

                          $(document).on('click', function(event) {
                            var $target = $(event.target);
                            if (!$target.closest('.bootstrap-select').length && $('.bootstrap-select').hasClass('open')) {
                                $('.bootstrap-select').removeClass('open show'); 
                                $('.dropdown-menu').removeClass('show'); 
                            }
                        });
                       });

                       function saveFilter() {
                        bootbox.prompt({
                            inputType: 'text',
                            placeholder: 'Enter the Title name',
                            title: "Save filters",
                            className: 'half-width-box',
                            buttons: {
                                confirm: { label: 'Yes', className: 'btn-success' },
                                cancel: { label: 'No', className: 'btn-danger' }
                            },
                            callback: function (remarks) {
                                if (remarks === null) return;

                                $('.bootbox .error-message').remove();
                                remarks = remarks.trim();

                                if (!remarks) {
                                    new PNotify({
                                        title: 'Missing Title',
                                        text: 'Please enter a name to save the filter.',
                                        type: 'error',
                                        addclass: 'custom-pnotify half-width-notify',
                                        cornerclass: '',
                                        animate: {
                                            animate: true,
                                            in_class: 'fadeInRight',
                                            out_class: 'fadeOutRight'
                                        },
                                        styling: 'bootstrap3',
                                        delay: 3000
                                    });
                                    return false;
                                }

                                if (remarks.length < 5 || remarks.length > 50) {
                                    setTimeout(() => {
                                        $('.bootbox-input').after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">Title must be between 5 and 50 characters.</div>`);
                                    }, 10);
                                    return false;
                                }

                                let duplicate = false;
                                $('#filter_types option').each(function () {
                                    if ($(this).text().trim().toLowerCase() === remarks.toLowerCase()) {
                                        duplicate = true;
                                        return false;
                                    }
                                });

                                if (duplicate) {
                                    setTimeout(() => {
                                        $('.bootbox-input').after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">A filter with this name already exists.</div>`);
                                    }, 10);
                                    return false;
                                }

                                let isInstallmentChecked = $('#installment_search').is(':checked');

                                const table = $('#fee_summary_data').DataTable();
                                  let visibleCols = [];
                                  table.columns().every(function (index) {
                                      if (this.visible()) {
                                          visibleCols.push(index);
                                      }
                                  });

                                  // Check if no columns are visible
                                  if (visibleCols.length === 0) {
                                      new PNotify({
                                          title: 'Action Required',
                                          text: 'Please click the "Get Report" button first before saving a filter.',
                                          type: 'warning',
                                          addclass: 'custom-pnotify half-width-notify',
                                          delay: 3000
                                      });
                                      return false; // Stop the saveFilter process
                                  }

                                let filterData = {
                                    title: remarks,
                                    installment_search: isInstallmentChecked,
                                    transaction_hide_show: $("#transaction_hide_show").is(":checked") ? 1 : 0,
                                    show_component_wise: $("#show_component_wise").is(":checked") ? 1 : 0,
                                    admission_status: $('#admission_status').val(),
                                    student_name_fees: $('#student_name_fees').val(),
                                    fee_type: $('#fee_type').val(),
                                    installment_type: $('#installment_type').val(),
                                    installmentId: $('#installmentId').val(),
                                    fee_type: $('#fee_type').val(),
                                    payment_status: $('#payment_status').val(),
                                    selectedColumns: $('#selectedColumns').val(),
                                    classId: $('#classId').val(),
                                    classSectionId: $('#classSectionId').val(),
                                    admission_type: $('#admission_type').val(),
                                    paymentModes: $('#paymentModes').val(),
                                    mediumId: $('#mediumId').val(),
                                    donorsId: $('#donorsId').val(),
                                    boardingId: $('#boardingId').val(),
                                    rte_nrteId: $('#rte_nrteId').val(),
                                    created_byId: $('#created_byId').val(),
                                    payment_modeJSON: $('#payment_modeJSON').val(),
                                    stopId: $('#stopId').val(),
                                    from_date: $('#from_date').val(),
                                    to_date: $('#to_date').val(),
                                    routeId: $('#routeId').val(),
                                    itemId: $('#itemId').val(),
                                    category: $('#category').val(),
                                    acad_year_id: $('#acad_year_id').val(),
                                    combination: $('#combination').val() || '',
                                    staff_kid: $('#staff_kid').val(),
                                    report_type: $('input[name="report_type"]:checked').val(),
                                    column_visibility: JSON.stringify(visibleCols), 
                                };

                                $.ajax({
                                    url: '<?php echo site_url('feesv2/reports/save_filters1'); ?>',
                                    type: 'POST',
                                    data: filterData,
                                    success: function (data) {
                                        if (data) {
                                            let lastId = 0;
                                            $('#filter_types option').each(function () {
                                                const val = parseInt($(this).val());
                                                if (!isNaN(val) && val > lastId) lastId = val;
                                            });

                                            const newId = lastId + 1;

                                            $('#filter_types').append(
                                                $('<option>', {
                                                    value: newId,
                                                    text: remarks
                                                })
                                            );

                                            $('#filter_types').val(newId).trigger('change');

                                            new PNotify({
                                                title: 'Success',
                                                text: 'Filter Saved Successfully',
                                                type: 'success',
                                                addclass: 'custom-pnotify half-width-notify'
                                            });
                                        } else {
                                            new PNotify({
                                                title: 'Error',
                                                text: 'Something went wrong',
                                                type: 'error',
                                                addclass: 'custom-pnotify half-width-notify'
                                            });
                                        }
                                    }
                                });
                            }
                        });

                        setTimeout(() => {
                            $('.bootbox .modal-dialog').css({
                                'max-width': '400px',
                                'margin': '1.75rem auto'
                            });

                            $('.bootbox-input').on('input', function () {
                                const inputVal = $(this).val().trim();
                                if (inputVal.length > 50) {
                                    $(this).val(inputVal.slice(0, 50));  // Truncate input to 50 characters
                                    $('.bootbox .error-message').remove(); // Remove previous error message
                                    $(this).after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">Title must be between 5 and 50 characters.</div>`);
                                } else {
                                    $('.bootbox .error-message').remove(); // Remove error message when valid length
                                }
                            });
                        }, 10);
                    }


                    function collectFilterData() {
                      const table = $('#fee_summary_data').DataTable(); 
                      let visibleCols = [];
                      table.columns().every(function (index) {
                          if (this.visible()) {
                              visibleCols.push(index);
                          }
                      });

                      return {
                          title: $('#remarks').val() || '',
                          installment_search: $('#installment_search').is(":checked"),
                          transaction_hide_show: $("#transaction_hide_show").is(":checked") ? 1 : 0,
                          show_component_wise: $("#show_component_wise").is(":checked") ? 1 : 0,
                          admission_status: $('#admission_status').val(),
                          student_name_fees: $('#student_name_fees').val(),
                          fee_type: $('#fee_type').val(),
                          installment_type: $('#installment_type').val(),
                          installmentId: $('#installmentId').val(),
                          fee_type: $('#fee_type').val(),
                          payment_status: $('#payment_status').val(),
                          selectedColumns: $('#selectedColumns').val(),
                          classId: $('#classId').val(),
                          classSectionId: $('#classSectionId').val(),
                          admission_type: $('#admission_type').val(),
                          paymentModes: $('#paymentModes').val(),
                          mediumId: $('#mediumId').val(),
                          donorsId: $('#donorsId').val(),
                          boardingId: $('#boardingId').val(),
                          rte_nrteId: $('#rte_nrteId').val(),
                          created_byId: $('#created_byId').val(),
                          payment_modeJSON: $('#payment_modeJSON').val(),
                          stopId: $('#stopId').val(),
                          from_date: $('#from_date').val(),
                          to_date: $('#to_date').val(),
                          routeId: $('#routeId').val(),
                          itemId: $('#itemId').val(),
                          category: $('#category').val(),
                          acad_year_id: $('#acad_year_id').val(),
                          combination: $('#combination').val() || '',
                          staff_kid: $('#staff_kid').val(),
                          report_type: $('input[name="report_type"]:checked').val(),
                          column_visibility: JSON.stringify(visibleCols) 
                      };
                  }



                  function updateFilter() {
                    const selectedFilterId = $('#filter_types').val();

                        if (!selectedFilterId) {
                            bootbox.alert({
                                title: "No Filter Selected",
                                message: "Please select a filter to update.",
                                className: "half-width-box",
                                buttons: {
                                    ok: {
                                        label: 'OK',
                                        className: 'btn-primary'
                                    }
                                }
                            });

                            // Center the alert modal
                            setTimeout(() => {
                                $('.bootbox .modal-dialog').css({
                                    'max-width': '400px',
                                    'margin': '1.75rem auto'
                                });
                            }, 10);

                            return; // stop the update function
                        }

                        let filterData = collectFilterData();
                        filterData.filter_types_id = selectedFilterId;
                        filterData.title = $('#filter_types option:selected').text().trim();
                        filterData.stakeholder_id = $('#stakeholder_id').val();

                        bootbox.confirm({
                            title: "Update Filters",
                            message: 'Are you sure you want to update the filter?',
                            className: "half-width-box",
                            buttons: {
                                confirm: { label: 'Yes', className: 'btn-success' },
                                cancel: { label: 'No', className: 'btn-danger' }
                            },
                            callback: function (result) {
                                if (result) {
                                    $.ajax({
                                        url: '<?php echo site_url('feesv2/reports/update_filters1'); ?>',
                                        type: 'POST',
                                        data: filterData,
                                        complete: function () {
                                            $.when(get_predefined_filters()).done(function () {
                                                if (
                                                    $('#filter_types option[value="' + filterData.filter_types_id + '"]').length === 0
                                                ) {
                                                    $('#filter_types').append(
                                                        $('<option>', {
                                                            value: filterData.filter_types_id,
                                                            text: filterData.title
                                                        })
                                                    );
                                                }

                                                $('#filter_types').val(filterData.filter_types_id);
                                                selectFilters();

                                                new PNotify({
                                                    title: 'Success',
                                                    text: 'Filter updated successfully.',
                                                    type: 'success',
                                                    addclass: 'custom-pnotify half-width-notify'
                                                });
                                            });
                                        }
                                    });
                                }
                            }
                        });

                        setTimeout(() => {
                            $('.bootbox .modal-dialog').css({
                                'max-width': '400px',
                                'margin': '1.75rem auto'
                            });
                        }, 10);
                }


                function get_predefined_filters() {
                        return $.ajax({
                            url: '<?php echo site_url('feesv2/reports/get_predefined_filters1'); ?>',
                            type: 'POST',
                            success: function(data) {
                                try {
                                     var res_data = JSON.parse(data);
                                      
                                    if (Array.isArray(res_data) && res_data.length > 0) {
                                        var html = '<option value="">Select</option>';
                                        res_data.forEach(filter => {
                                            html += `<option value="${filter.id}">${filter.title}</option>`;
                                          });

                                        $('#filter_types').html(html);
                                      } else {
                                          console.warn("No predefined filters found.");
                                        $('#filter_types').html('<option value="">No Filters Available</option>');
                                      }
                                  } catch (error) {
                                    console.error("Error parsing response:", error);
                                    $('#filter_types').html('<option value="">Error Loading Filters</option>');
                                  }
                              },
                              error: function(xhr, status, error) {
                                console.error("AJAX Error:", error);
                                $('#filter_types').html('<option value="">Error Fetching Filters</option>');
                              }
                          });
              }

              function selectFilters() {
                      const filterId = $('#filter_types').val();

                      if (!filterId || filterId.trim() === '') {
                        $('#reload_filter').prop('disabled', true); 
                        return;
                      } else {
                        $('#reload_filter').prop('disabled', false);
                      }

                      $('#reload_filter').show();

                      $.ajax({
                        url: '<?php echo site_url('feesv2/reports/get_predefined_filters_by_id1'); ?>',
                        type: 'POST',
                        data: { filter_id: filterId },
                        dataType: 'json',
                        success: function (response) {
                          if (response && response.success && response.filters_selected) {
                            const filters = response.filters_selected;

                            const updateSelect = (selector, values) => {
                              if (values !== undefined && values !== null) {
                                if (typeof values === 'string') values = values.split(',');
                                $(selector).val(values);
                                $(selector).selectpicker?.('refresh');
                              }
                            };

                            $('#installment_search')
                              .prop('checked', filters.installment_search === "true")
                              .trigger('change');

                            $('#show_over_due').prop('checked', filters.show_over_due === "true");
                            $('#transaction_hide_show').prop('checked', filters.transaction_hide_show == "1").trigger('change');
                            $('#show_component_wise').prop('checked', filters.show_component_wise == "1");

                            updateSelect('#fee_type', filters.fee_type);
                            updateSelect('#admission_status', filters.admission_status);
                            updateSelect('#combination', filters.combination);
                            updateSelect('#rte_nrteId', filters.rte_nrteId);
                            updateSelect('#staff_kid', filters.staff_kid);
                            updateSelect('#payment_status', filters.payment_status);
                            updateSelect('#donorsId', filters.donorsId);
                            updateSelect('#paymentModes', filters.paymentModes);
                            updateSelect('#admission_type', filters.admission_type);

                            $('#student_name_fees').val(filters.student_name_fees);

                            const classSectionFlow = new Promise((resolve) => {
                              updateSelect('#classId', filters.classId);
                              $.ajax({
                                url: '<?php echo site_url('feesv2/reports_v2/get_class_section_by_fees_selection_class') ?>',
                                type: 'POST',
                                data: { feeclass: filters.classId },
                                success: function (data) {
                                  const resdata = JSON.parse(data);
                                  let option = '';
                                  resdata.forEach(item => {
                                    option += `<option value="${item.section_id}">${item.class_section}</option>`;
                                  });
                                  $('#classSectionId').html(option);
                                  $('#classSectionId').selectpicker('refresh');
                                  $('#classSectionId').val(filters.classSectionId).selectpicker('refresh');
                                  resolve();
                                }
                              });
                            });

                            let installmentFlow = Promise.resolve();

                            if (filters.installment_search === "true") {
                              $('#installmentType').show();
                              $('#installment').show();

                              installmentFlow = new Promise((resolve) => {
                                setTimeout(() => {
                                  updateSelect('#fee_type', filters.fee_type);
                                  resolve();
                                }, 200);
                              })
                              .then(() => {
                                return new Promise((resolve) => {
                                  setTimeout(() => {
                                    $.ajax({
                                      url: '<?php echo site_url('reports/student/student_report/get_bpTypewise_insType') ?>',
                                      type: 'POST',
                                      data: { bpType: filters.fee_type },
                                      success: function (data) {
                                        const parsed = JSON.parse(data);
                                        let output = '<option value="">Select Installments Type</option>';
                                        parsed.forEach(item => {
                                          output += `<option value="${item.id}">${item.name}</option>`;
                                        });
                                        $('#installment_type').html(output).val(filters.installment_type).trigger('change');
                                        resolve();
                                      }
                                    });
                                  }, 200);
                                });
                              })
                              .then(() => {
                                return new Promise((resolve) => {
                                  setTimeout(() => {
                                    $.ajax({
                                      url: '<?php echo site_url('feesv2/reports/installment_type_installment') ?>',
                                      type: 'POST',
                                      data: { installment_type: filters.installment_type },
                                      success: function (data) {
                                        const parsed = JSON.parse(data);
                                        let output = '';
                                        parsed.forEach(item => {
                                          output += `<option value="${item.id}">${item.name}</option>`;
                                        });
                                        $('#installmentId').html(output);
                                        $('#installmentId').selectpicker();
                                        $('#installmentId').val(filters.installmentId).selectpicker('refresh');
                                        resolve();
                                      }
                                    });
                                  }, 200);
                                });
                              });
                            }

                            if (filters.report_type) {
                              $(`input[name="report_type"][value="${filters.report_type}"]`).prop('checked', true);
                              $('#getReport').trigger('click');
                            }

                            Promise.all([installmentFlow, classSectionFlow]).then(() => {
                              $('#search').prop('disabled', true).val('Please wait...');
                              get_report();

                              if (filters.column_visibility) {
                                setTimeout(() => {
                                  applySavedColumnVisibility(filters.column_visibility);
                                }, 1000);
                              }
                            });

                          } else {
                            alert('Failed to fetch filter details. Please try again.');
                          }
                        }
                      });
                    }


                function applySavedColumnVisibility(column_visibility) {
                    const tableId = '#fee_summary_data';
                    let visibleCols;

                    try {
                      visibleCols = JSON.parse(column_visibility || '[]');
                    } catch (err) {
                      console.error('Failed to parse column_visibility JSON:', err);
                      return;
                    }

                    const waitForDataTable = () => {
                      const maxTries = 10;
                      let attempts = 0;

                      const interval = setInterval(() => {
                        if ($.fn.DataTable.isDataTable(tableId)) {
                          const table = $(tableId).DataTable();
                          table.columns().every(function (index) {
                            const shouldBeVisible = visibleCols.includes(index);
                            this.visible(shouldBeVisible);
                          });
                          clearInterval(interval);
                        } else if (++attempts >= maxTries) {
                          clearInterval(interval);
                          console.warn('⚠️ DataTable not initialized after max attempts');
                        }
                      }, 300);
                    };

                    waitForDataTable();
            }
 
         </script>
        <?php } ?>
      </div>
    <?php } ?>
  </div>


      </div>

    </div>

    <div class="row">
      <div class="col-sm-12 col-md-12 text-center">
        <input type="button" onclick="get_report()" name="search" id="search" class="btn btn-primary" value="Get Report">
      </div>
    </div>

     <div class="text-center"><div style="display: none;" class="progress" id="progress"><div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div></div></div>

      <ul class="panel-controls" id="exportButtons" style="display: none;">
        <!-- <button id="stu_print" class="btn btn-danger" onclick="printProfile()"><span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print</button> -->
        <a style="margin-left:3px;" onclick="exportToExcel_daily()" class="btn btn-primary pull-right">Export</a>
      </ul>

      <div id="printArea">
        <div id="print_visible" style="display: none;" class="text-center">
          <h3><?php echo $this->settings->getSetting('school_name') ?></h3>
          <h4>Fees summary Report</h4>
        </div>
         
        <div class="col-12 text-center loading-icon" style="display: none;">
          <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
        </div>
        <div class="fee_summary">
        </div>
      <ul class="panel-controls" style="width:300px" id="range-input"></ul><br>

       
        <div class="table-responsive" id="report_width_container">
        </div>
      </div>

    </div>
  </div>
<div id="modalBackdrop" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background-color:rgba(0,0,0,0.2); z-index:999;" onclick="closeDisturbanceInfo()"></div>

<div id="disturbanceInfoModal" style="display:none; position:absolute; top:510px!important; left:530.575px!important; background:#fff; width:280px; font-family:'Poppins', sans-serif; padding:1rem 1.2rem 1.2rem; border-radius:10px; box-shadow:0 10px 30px rgba(0,0,0,0.15); z-index:1000; color:#333;">
  <div style="position:absolute; left:-8px; top:20px; width:0; height:0; border-top:7px solid transparent; border-bottom:7px solid transparent; border-right:8px solid #fff;"></div>
  <div style="font-size:0.95rem; line-height:1.5; color:#555;">
    <ul style="padding-left: 18px; margin: 0;">
      <li>This is the <strong>balance amount</strong> to be <strong>paid/recovered</strong> from the <strong>Non-Continuing students</strong> (those who are <strong>not promoted to this year</strong>).</li>
    </ul>
  </div>
</div>


</div>

<script>
let hoverTimeout;

function openDisturbanceInfo(event) {
  clearTimeout(hoverTimeout); 

  const modal = document.getElementById('disturbanceInfoModal');
  const backdrop = document.getElementById('modalBackdrop');
  const icon = event.currentTarget;

  const rect = icon.getBoundingClientRect();
  const scrollTop = window.scrollY;
  const scrollLeft = window.scrollX;


  modal.style.display = 'block';
  backdrop.style.display = 'block';

  // Keep modal open while hovering over it
  modal.addEventListener('mouseenter', () => clearTimeout(hoverTimeout));
  modal.addEventListener('mouseleave', closeDisturbanceInfo);
}

function delayedClose() {
  hoverTimeout = setTimeout(() => {
    closeDisturbanceInfo();
  }, 300); // delay in ms to allow modal mouseenter
}
function closeDisturbanceInfo() {
  document.getElementById('disturbanceInfoModal').style.display = 'none';
  document.getElementById('modalBackdrop').style.display = 'none';
}
</script>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>

<script type="text/javascript">
  var loan_column = '<?php echo $loan_column ?>';
  var adjustment = '<?php echo $fee_adjustment_amount ?>';
  var fineAmount = '<?php echo $this->settings->getSetting('fee_fine_amount') ?>';
  var is_semester_scheme = '<?php echo $this->settings->getSetting('is_semester_scheme') ?>';
  var combination_column = '<?php echo $this->settings->getSetting('fees_stuent_summary_report_show_combination_filter') ?>';
  var cohortStudentIds = [];
  var completed = 0;
  var total_students = 0;
  var totalFeeAssingedStudentCount = 0;
  var aluminiCount = 0;
  var summaryData = [];
  var previous_data = 0;
  var excess_data = 0;
  var prevousYearname = '';
  var continue_students_previous_amount = 0;
  var continue_non_students_previous_amount = 0;
  $(document).ready(function(){
    var fee_type = $('#fee_type').val();
    var clsId =  $('#classId').val();
    if (fee_type != null || clsId != null) {
      get_report();
    }

    // Handle component wise checkbox change
    $('#show_component_wise').change(function() {
      if ($(this).is(':checked')) {
        // Show component wise report
        $('.fee_summary').html('');
        $('.table-responsive').html('');
      }
    });
  });
  function get_report() {
    // Check if component wise is selected
    var isComponentWise = $('#show_component_wise').is(':checked');

    if (isComponentWise) {
      get_component_wise_report();
      return;
    }

    $(".loading-icon").show();
    $("#exportButtons").hide();
    $('.table-responsive').html('');
    $('.fee_summary').html('');
    $('.total_student_summary').hide();
    $('#search').prop('disabled',true).val('Please wait..');
    $('select').prop('disabled', true);
    $('.selectpicker').prop('disabled', true);
     
    var fee_type = $('#fee_type').val();
    var payment_status = $('#payment_status').val();
    var selectedColumns = $('#selectedColumns').val();
    var clsId =  $('#classId').val();
    var classSectionId =  $('#classSectionId').val();
    var admission_type = $('#admission_type').val(); 
    var paymentModes = $('#paymentModes').val();
    var mediumId = $('#mediumId').val();
    var donorsId = $('#donorsId').val();
    var boardingId = $('#boardingId').val();
    var rte_nrteId = $('#rte_nrteId').val();
    var created_byId = $('#created_byId').val();
    var payment_modeJSON = $('#payment_modeJSON').val();
    var stopId = $('#stopId').val();
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    var routeId = $('#routeId').val();
    var itemId = $('#itemId').val();
    var category = $('#category').val();
    var acad_year_id = $('#acad_year_id').val();
    var combination = $('#combination').val();
    var installment_type = $('#installment_type').val();
    var installmentId = $('#installmentId').val();
    var admission_status = $('#admission_status').val();
    var staff_kid = $('#staff_kid').val();
    
    if (combination == undefined) {
      combination = '';
    }
    completed = 0;
    total_students = 0;
    totalFeeAssingedStudentCount = 0;
    aluminiCount = 0;
    summaryData = [];
    previous_data = 0;
    excess_data = 0;
    continue_students_previous_amount = 0;
    continue_non_students_previous_amount = 0;
    $.ajax({
      url: '<?php echo site_url('feesv2/reports_v2/getStudentsForSummary_v2'); ?>',
      data: {'clsId': clsId,'admission_type':admission_type,'paymentModes':paymentModes,'fee_type':fee_type,'mediumId':mediumId,'donorsId':donorsId,'boardingId':boardingId,'created_byId':created_byId,'payment_modeJSON':payment_modeJSON,'selectedColumns':selectedColumns,'from_date':from_date,'to_date':to_date,'routeId':routeId,'itemId':itemId,'stopId':stopId,'payment_status':payment_status,'category':category,'classSectionId':classSectionId,'rte_nrteId':rte_nrteId,'acad_year_id':acad_year_id,'combination':combination,'installment_type':installment_type,'installmentId':installmentId,'admission_status':admission_status,'staff_kid':staff_kid},
      type: "post",
      success: function (data) {
        // console.log(data);
         var cohort_student_ids = JSON.parse(data);
          if (cohort_student_ids.length == 0) {
            $('.table-responsive').html('<h3>Data not found</h3>');
            $(".loading-icon").hide();
            $('#search').prop('disabled',false).val('Get Report');
            $('select').prop('disabled', false);
            $('.selectpicker').prop('disabled', false);
          }

          cohortStudentIds = cohort_student_ids;
          total_students = parseInt(150* (cohortStudentIds.length - 2)) + parseInt(cohortStudentIds[cohortStudentIds.length - 1].length);
          var progress = document.getElementById('progress-ind');
          progress.style.width = (completed/total_students)*100+'%';
          $("#progress").show();
          // console.log(cohortStudentIds);
          // setTimeout(function(){ callReportGetter(0); }, 500);
          callReportGetter(0);

        // $('#feeGenerationReport').html(data);
      },
      error: function (err) {
        alert('Network may be slow');
        console.log(err);
      }
    });
  }

  function callReportGetter(index){
     if(index < cohortStudentIds.length) {
      getReport(index);
    } else {
      $("#progress").hide();
      $(".loading-icon").hide();
      $("#exportButtons").show();
      $('.total_student_summary').show();
      $('#search').prop('disabled',false).val('Get Report');
      $('select').prop('disabled', false);
      $('.selectpicker').prop('disabled', false);
      var fee_summary ='<table class="table table-bordered" style="width:40%">';
      fee_summary +='<thead>';
      fee_summary +='<tr>';
      fee_summary +='<th colspan="2">Previous Balance Amount ('+prevousYearname+') </th>';
      fee_summary +='</tr>';

      fee_summary +='<tr>';
      fee_summary +='<td><b>Balance - Continuing Students</b></td>';
      fee_summary +='<td><b>'+in_currency(continue_students_previous_amount)+'</b></th>';
      fee_summary +='</tr>';
      
      fee_summary += '<tr>';
      fee_summary += '<td><b>Balance - Non-continuing Students</b></td>';
      fee_summary += '<td><b>' + in_currency(continue_non_students_previous_amount) + '<br><a href="javascript:void(0)" class="distrubanceAmount" onclick="check_distrubance_details()">Disturbance Amount</a> <i class="fa fa-info-circle" style="cursor:pointer; color:blue; font-size:15px" onmouseenter="openDisturbanceInfo(event)" onmouseleave="delayedClose()"></i></b></td>';
      fee_summary += '</tr>';


      fee_summary +='</table>';

      fee_summary +='<table class="table table-bordered" style = "width : 97%">';
      fee_summary +='<thead>';

      fee_summary +='<tr>';
      fee_summary +='<th>Fee Type</th>';
      fee_summary +='<th># Student Assigned</th>';
      fee_summary +='<th>Excess Amount</th>';
      fee_summary +='<th>Fee Amount</th>';
      fee_summary +='<th>Collected Amount</th>';
      fee_summary +='<th>Total Concession</th>';
      if (adjustment) {
        fee_summary +='<th>Total Adjustment</th>';
      }
      if (fineAmount) {
        fee_summary +='<th>Total Fine (Collected)</th>';
      }
      if (loan_column !=0) {
        fee_summary +='<th>Loan Provider Charge</th>';
      }
      fee_summary +='<th>Discount</th>';
      fee_summary +='<th>Balance</th>';
      fee_summary +='</tr>';
      fee_summary +='</thead>';
      fee_summary +='<tbody>';
      var gtotal_fee_amount = 0;
      var gtotal_fee_collected = 0;
      var gtotal_fee_concession = 0;
      var gtotal_fee_received_concession = 0;
      var gtotal_fee_applied_concession = 0;
      var gtotal_fee_applied_adjustment = 0;
      var gtotal_fee_applied_fine = 0;
      var gtotal_fee_balance = 0;
      var gtotal_total_students = 0;
      var gloan_provider_charges = 0;
      var gloan_total_discount = 0;
      // var gloan_total_previous_bal = 0;
      // var gloan_total_excess_amount = 0;
      for(var bp_name in summaryData) {
        gtotal_fee_amount += summaryData[bp_name]['total_fee_amount'];
        gtotal_fee_collected += summaryData[bp_name]['total_fee_collected'];
        gtotal_fee_concession += summaryData[bp_name]['total_fee_concession'];
        // gtotal_fee_received_concession += summaryData[bp_name]['total_fee_received_concession'];
        gtotal_fee_applied_concession += summaryData[bp_name]['total_fee_applied_concession'];
        gtotal_fee_applied_adjustment += summaryData[bp_name]['total_adjustment_applied'];
        gtotal_fee_applied_fine += summaryData[bp_name]['total_fine_applied'];
        gtotal_fee_balance += summaryData[bp_name]['total_fee_balance'];
        gtotal_total_students += summaryData[bp_name]['total_student_assigned'];
        gloan_provider_charges += summaryData[bp_name]['loan_provider_charges'];
        gloan_total_discount += summaryData[bp_name]['total_discount'];
        // gloan_total_previous_bal += summaryData[bp_name]['previous_balance'];
        // gloan_total_excess_amount += summaryData[bp_name]['excess_amount'];

        fee_summary += '<tr>';
        fee_summary +='<th style="font-size:14px; color:#7ea3d2"><b>'+bp_name+'</b></th>';
        fee_summary +='<td>'+in_currency(summaryData[bp_name]['total_student_assigned'])+'</td>';
        fee_summary +='<td>-</td>';
        fee_summary +='<td>'+in_currency(summaryData[bp_name]['total_fee_amount'])+'</td>';
        fee_summary +='<td>'+in_currency(summaryData[bp_name]['total_fee_collected'] - summaryData[bp_name]['loan_provider_charges'] - summaryData[bp_name]['total_discount'])+'</td>';
        fee_summary +='<td> ( '+in_currency(summaryData[bp_name]['total_fee_concession'])+' ) </td>';
        // fee_summary +='<td>'+in_currency(summaryData[bp_name]['total_fee_received_concession'])+'</td>';
        // fee_summary +='<td> ( '+in_currency(summaryData[bp_name]['total_fee_applied_concession'])+' )</td>';
        if (adjustment) {
          fee_summary +='<td> ( '+in_currency(summaryData[bp_name]['total_adjustment_applied'])+' ) </td>';
        }
        if (fineAmount) {
          fee_summary +='<td> '+in_currency(summaryData[bp_name]['total_fine_applied'])+' </td>';
        }
        if (loan_column !=0) {
          fee_summary +='<td>'+in_currency(summaryData[bp_name]['loan_provider_charges'])+'</td>';
        }
        fee_summary +='<td>('+in_currency(summaryData[bp_name]['total_discount'])+')</td>';
        fee_summary +='<td>'+in_currency(summaryData[bp_name]['total_fee_balance'])+'</td>';
        fee_summary += '</tr>';
      }
      fee_summary +='</tbody>';
      fee_summary +='<tfoot>';
      fee_summary +='<tr>';
      fee_summary +='<th colspan="2" style="font-size:14px;">Grand Total</th>';
      fee_summary +='<th>('+in_currency(excess_data)+')</th>';
      fee_summary +='<th>'+in_currency(gtotal_fee_amount)+'</th>';
      fee_summary +='<th>'+in_currency(gtotal_fee_collected - gloan_total_discount)+'</th>';
      fee_summary +='<th> ( '+in_currency(gtotal_fee_concession)+' ) </th>';
      // fee_summary +='<th>'+in_currency(gtotal_fee_received_concession)+'</th>';
      // fee_summary +='<th> ( '+in_currency(gtotal_fee_applied_concession)+' ) </th>';

      if (adjustment) {
        fee_summary +='<th> ( '+in_currency(gtotal_fee_applied_adjustment)+' ) </th>';
      }
      if (fineAmount) {
        fee_summary +='<td> '+in_currency(gtotal_fee_applied_fine)+' </td>';
      }

      if (loan_column !=0) {
        fee_summary +='<th>'+in_currency(gloan_provider_charges)+'</th>';
      }
      fee_summary +='<th>('+in_currency(gloan_total_discount)+')</th>';
      fee_summary +='<th>'+in_currency(gtotal_fee_balance)+'</th>';
      fee_summary +='</tr>';
      fee_summary +='<tr>';

      var colSpan = '7';
      if(adjustment || fineAmount || loan_column){
        colSpan = '8';
      }
      if((fineAmount && loan_column) || (adjustment && fineAmount && loan_column)){
        colSpan = '9';
      }
      if(adjustment && fineAmount && loan_column){
        colSpan = '10';
      }
      fee_summary +='<th colspan="'+colSpan+'" style="font-size:14px;">Over all Balance (Previous Continuing Balance Amount + Current Year Balance - Excess Amount)</th>';
      fee_summary +='<th>'+in_currency((gtotal_fee_balance + continue_students_previous_amount) - excess_data)+'</th>';
      fee_summary +='</tr>';
      fee_summary +='</tfoot>';

      fee_summary +='</table>';
      $(".fee_summary").html(fee_summary);
      var transactionHideShow = $("#transaction_hide_show").is(":checked") ? 0 : 1;

      if(!transactionHideShow){
      if ($.fn.DataTable.isDataTable('#fee_summary_data')) {
        $('#fee_summary_data').DataTable().destroy(); 
        $('#fee_summary_data').empty(); 
      }
      let colLen = 13;
      if (combination_column == 1 && is_semester_scheme == 1) {
          colLen = 15;
      } else if (combination_column == 1 || is_semester_scheme == 1) {
          colLen = 14;
      }
      let table = $('#fee_summary_data').DataTable({
        ordering: false,
        paging: false, 
        dom: 'Bfrtip', 
        info: false,
        'columnDefs':  [
          { orderable: false, targets: 1 },
          {targets : 4, visible: false},
          {targets : 5, visible: false},
        ],
        // scrollY: "500px", 
        // scrollX: true, 
        // scrollCollapse: true,
        buttons: [
          {
            extend: 'print',
            text: '<button class="btn btn-info"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
            title: 'Fee detail report',
            footer: true,
            exportOptions: {
              columns: ':visible', 
            },
            customize: function (win) {
            $(win.document.body)
              .prepend($('.fee_summary').clone())
              .css('font-family', "'Poppins', sans-serif")
              .css('font-size', '10pt')
              .css('padding', '10px');

            $(win.document.head).append(`
              <style>
                @page {
                  size: auto;
                  margin: 12mm;
                }

                body {
                  font-family: 'Poppins', sans-serif;
                  -webkit-print-color-adjust: exact;
                  print-color-adjust: exact;
                  color: #333;
                  background: #fff;
                }

                h2, h3 {
                  text-align: center;
                  margin-bottom: 15px;
                  font-weight: 500;
                }

                table {
                  border-collapse: collapse !important;
                  width: 100% !important;
                  margin-top: 20px;
                  font-size: 10pt;
                  color: #333;
                }

                th, td {
                  border: 1px solid #ccc !important;
                  padding: 8px 12px;
                  text-align: left;
                  vertical-align: middle;
                }

                th {
                  background-color: #f4f7fc !important;
                  font-weight: 600;
                  color: #333;
                }

                .table-bordered {
                  width: 100% !important;
                }

                .fee_summary table {
                  margin-bottom: 25px;
                }

                .fee_summary th[colspan] {
                  text-align: center;
                  background: #e0ecff;
                  font-size: 11pt;
                  font-weight: 500;
                }

                tfoot th {
                  background-color: #f9f9f9;
                  font-weight: 600;
                }

                .distrubanceAmount {
                  color: #007bff;
                  text-decoration: underline;
                  font-size: 10pt;
                }

                a {
                  color: #007bff !important;
                }
              </style>
            `);
          },
          },
          {
            text: '<button class="btn btn-info"><span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel</button>',
            title: 'Fee detail report',
            action: function () {
              exportToExcel_fee_status();
            },
          },
        ],
      });
      initializeColVisButton(table, 'fee_summary_data_wrapper', colLen);
      if (transactionHideShow) {
        $('#exportButtons').show(); 
      } else {
        $('#exportButtons').hide();
      }
    }
    }
  }
  function exportToExcel_fee_status() {
    var htmls = "";
    var uri = "data:application/vnd.ms-excel;base64,";
    var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="(link unavailable)">' +
      '<head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->' +
      '<meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
    var base64 = function (s) {
        return window.btoa(unescape(encodeURIComponent(s)));
    };
    var format = function (s, c) {
        return s.replace(/{(\w+)}/g, function (m, p) {
            return c[p];
        });
    };

    var mainTable = $("#fee_summary_data").clone();
    var summaryTable = $(".fee_summary").clone();

    // Remove unwanted elements
    // mainTable.find("#fee_summary_data_filter").remove();
    // mainTable.find("#fee_summary_data_summary_filter").remove();
    mainTable.find(".dt-buttons").remove();
    summaryTable.find(".distrubanceAmount").remove();
    
    var titleRow = '';
    htmls = titleRow + summaryTable.prop("outerHTML") + "<br>" +mainTable.prop("outerHTML");
    var ctx = { worksheet: "Spreadsheet", table: htmls };
    if (navigator.msSaveOrOpenBlob) {
        var blob = new Blob([format(template, ctx)], { type: "application/vnd.ms-excel" });
        navigator.msSaveOrOpenBlob(blob, "fees_detail_report.xls");
    } else {
        var link = document.createElement("a");
        link.download = "fees_detail_report.xls";
        link.href = uri + base64(format(template, ctx));
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}


function initializeColVisButton(table, wrapperId, colLen) {
  // Add a column visibility button to the given table
  new $.fn.dataTable.Buttons(table, {
    buttons: [
      {
        extend: 'colvis',
        text: '<button class="btn btn-info"><span class="fa fa-columns" aria-hidden="true"></span> Columns</button>',
        className: 'btn btn-info',
        columns: function (idx, data, node) {
          return idx <= colLen; // Restrict to columns <= 13
        },
      },
    ],
  }).container().appendTo($(`#${wrapperId} label`));
}

  function getReport(index){
    var cohortstudentids = cohortStudentIds[index];
    var fee_type = $('#fee_type').val();
    var payment_status = $('#payment_status').val();
    var selectedColumns = $('#selectedColumns').val();
    var clsId =  $('#classId').val();
    var classSectionId =  $('#classSectionId').val();
    var admission_type = $('#admission_type').val(); 
    var paymentModes = $('#paymentModes').val();
    var mediumId = $('#mediumId').val();
    var donorsId = $('#donorsId').val();
    var boardingId = $('#boardingId').val();
    var rte_nrteId = $('#rte_nrteId').val();
    var created_byId = $('#created_byId').val();
    var payment_modeJSON = $('#payment_modeJSON').val();
    var stopId = $('#stopId').val();
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    var routeId = $('#routeId').val();
    var itemId = $('#itemId').val();
    var category = $('#category').val();
    var acad_year_id = $('#acad_year_id').val();
    var combination = $('#combination').val();
    if (combination == undefined) {
      combination = '';
    }
    var installment_type = $('#installment_type').val();
    var installmentId = $('#installmentId').val();
    var admission_status = $('#admission_status').val();
    var staff_kid = $('#staff_kid').val();
    var transaction_hide_show = $("#transaction_hide_show").is(":checked") ? 1 : 0;
    // console.log(cohortstudentids);
    url = '<?php echo site_url('feesv2/reports_v2/getStudentsForSummary_v2_details_new'); ?>';
    $.ajax({
      url:url,
      type: 'post',
      data: {cohortstudentids:cohortstudentids,'clsId': clsId,'admission_type':admission_type,'paymentModes':paymentModes,'fee_type':fee_type,'mediumId':mediumId,'donorsId':donorsId,'boardingId':boardingId,'rte_nrteId':rte_nrteId,'created_byId':created_byId,'payment_modeJSON':payment_modeJSON,'selectedColumns':selectedColumns,'from_date':from_date,'to_date':to_date,'routeId':routeId,'itemId':itemId,'stopId':stopId,'payment_status':payment_status,'category':category,'classSectionId':classSectionId,'rte_nrteId':rte_nrteId,'acad_year_id':acad_year_id,'combination':combination,'installment_type':installment_type,'installmentId':installmentId,'admission_status':admission_status, 'staff_kid':staff_kid,'transaction_hide_show':transaction_hide_show},
      success:function(data){
        var rData = $.parseJSON(data);
        var headers = rData.headers;
        var header = rData.header;
        var fee_data = rData.fee_data;
        var summary = rData.summary;
        var transactions = rData.transactions;
        var headerbpName = rData.summaryHeaderbp;
        prevousYearname = rData.prevousYearname;
        continue_non_students_previous_amount = rData.prevousNonContinueCount;

        // Handle component data if available
        var component_headers = rData.component_headers || {};
        var has_component_data = rData.has_component_data || false;
        var component_wise_data = rData.component_wise_data || [];
        var dual_display_ready = rData.dual_display_ready || false;

        // Check if user wants component-wise view
        var show_component_wise = $("#show_component_wise").is(":checked");

        // console.log(summary);
        constructFeeSummary(summary, headers,index, headerbpName);

        if(index == 0) {
          if (show_component_wise && dual_display_ready) {
            constructComponentWiseHeader(component_headers);
          } else {
            constructFeeHeader(header);
          }
        }

        completed += Object.keys(fee_data).length;
        var progress = document.getElementById('progress-ind');
        progress.style.width = (completed/total_students)*100+'%';

        // Display based on user selection
        if (show_component_wise && dual_display_ready) {
          constructComponentWiseReport(component_wise_data, component_headers, index);
        } else {
          constructFeeReport(fee_data, headers, index, transactions, transaction_hide_show, component_headers, has_component_data);
        }

        // $('.table-responsive').html(data);
      }
    });
  }

  
  function constructFeeSummary(summary, headers,index, headerbpName, total_assign_std) {
    for(bp in headerbpName){
      if ((summary).hasOwnProperty(bp)) {
        if (!(summaryData).hasOwnProperty(headerbpName[bp].blueprint_name)) {
          summaryData[headerbpName[bp].blueprint_name] = [];
          summaryData[headerbpName[bp].blueprint_name]['total_fee_amount'] = 0;
          summaryData[headerbpName[bp].blueprint_name]['total_fee_collected'] = 0;
          summaryData[headerbpName[bp].blueprint_name]['total_fee_concession'] = 0;
          summaryData[headerbpName[bp].blueprint_name]['total_fee_applied_concession'] = 0;
          summaryData[headerbpName[bp].blueprint_name]['total_fee_received_concession'] = 0;
          summaryData[headerbpName[bp].blueprint_name]['total_fee_balance'] = 0;
          summaryData[headerbpName[bp].blueprint_name]['loan_provider_charges'] = 0;
          summaryData[headerbpName[bp].blueprint_name]['total_student_fee_assigned'] = 0;
          summaryData[headerbpName[bp].blueprint_name]['total_student_assigned'] = 0;
          summaryData[headerbpName[bp].blueprint_name]['total_adjustment_applied'] = 0;
          summaryData[headerbpName[bp].blueprint_name]['total_fine_applied'] = 0;
          summaryData[headerbpName[bp].blueprint_name]['total_discount'] = 0;
          // summaryData[headerbpName[bp].blueprint_name]['previous_balance'] = 0;
          // summaryData[headerbpName[bp].blueprint_name]['excess_amount'] = 0;
        }
        summaryData[headerbpName[bp].blueprint_name]['total_fee_amount'] += summary[bp].total_fee_summary;
        summaryData[headerbpName[bp].blueprint_name]['total_fee_collected'] += summary[bp].total_fee_paid_summary;
        summaryData[headerbpName[bp].blueprint_name]['total_fee_concession'] += summary[bp].total_concession_summary;
        summaryData[headerbpName[bp].blueprint_name]['total_fee_applied_concession'] += summary[bp].applied_concession_summary;
        summaryData[headerbpName[bp].blueprint_name]['total_fee_received_concession'] += summary[bp].received_concession_summary;
        summaryData[headerbpName[bp].blueprint_name]['total_fee_balance'] += summary[bp].total_balance_summary;
        summaryData[headerbpName[bp].blueprint_name]['loan_provider_charges'] += summary[bp].loan_provider_charges;
        summaryData[headerbpName[bp].blueprint_name]['total_student_assigned'] += summary[bp].total_assigned;
        summaryData[headerbpName[bp].blueprint_name]['total_adjustment_applied'] += summary[bp].applied_adjustment_summary;
        summaryData[headerbpName[bp].blueprint_name]['total_fine_applied'] += summary[bp].applied_fine_summary;
        summaryData[headerbpName[bp].blueprint_name]['total_discount'] += summary[bp].applied_discount;
        // summaryData[headerbpName[bp].blueprint_name]['previous_balance'] += summary[bp].previous_balance;
        // summaryData[headerbpName[bp].blueprint_name]['excess_amount'] += summary[bp].excess_amount;
      }
    }
  }

  function constructFeeHeader(header) {
    var h_output = '<div style="width: 100%; overflow: auto;"><table id="fee_summary_data" class="table table-bordered">';
    h_output += header;
    h_output += '</table></div>';
    $('.table-responsive').html(h_output);
    add_scroller('report_width_container');

  }
  function constructFeeReport(fee_data, headers, index, transactions, transaction_hide_show, component_headers, has_component_data) {
    // Set default values for component parameters if not provided (backward compatibility)
    component_headers = component_headers || {};
    has_component_data = has_component_data || false;
    //console.log('trans',transaction_hide_show);
    var srNo = index * 150;
    var html = '';
    if(!transaction_hide_show){
      html +='<tbody>';
    }
    if(transaction_hide_show){
      var tbody = $('#fee_summary_data tbody');
      if (tbody.length === 0) {
        tbody = $('<tbody></tbody>');
        $('#fee_summary_data').append(tbody);
      }
    }

    var m=0;
    var status_arr = {
      'NOT_STARTED' : '<span class="text-danger">Not Paid</span>',
      'FULL' : '<span class="text-success">Paid</span>',
      'PARTIAL' : '<span class="text-warning">Partially-paid</span>'
    };
    for (var k in fee_data) {
      continue_students_previous_amount += parseFloat(fee_data[k].previous_bal);
      excess_data += parseFloat(fee_data[k].addt_amount);
      if (fee_data[k].admission_status !='2') {
        statusColor ='#d30c0c';
        aluminiCount++;
      }else{
        statusColor ='';
      }
      html += '<tr style="color:'+statusColor+'" >';
      html += '<td>'+(m+1+srNo)+'</td>';
      html += '<td>'+fee_data[k].student_name+'</td>';
      html += '<td>'+fee_data[k].className+'</td>';
      html += '<td>'+fee_data[k].sectionName+'</td>';
      html += '<td>'+fee_data[k].category+'</td>';
      html += '<td>'+fee_data[k].caste+'</td>';
      if (is_semester_scheme == 1) {
        if (fee_data[k].semester == null) {
          html +='<td></td>';
        }else{
          html += '<td>'+fee_data[k].semester+'</td>';
        }
      }

      html += '<td>'+fee_data[k].admission_no+'</td>';
      html += '<td>'+fee_data[k].enrollment_number+'</td>';
      html += '<td>'+fee_data[k].boarding_type+'</td>';
      html += '<td>'+fee_data[k].application_no+'</td>';
      if(combination_column == 1){
        html += '<td>'+fee_data[k].combination+'</td>';
      }
      html += '<td>'+fee_data[k].parent_name+'</td>';
      html += '<td>'+fee_data[k].mobile_no+'</td>';
      var previous_bal = fee_data[k].previous_bal;
      if (fee_data[k].previous_bal == null || fee_data[k].previous_bal == undefined ) {
        previous_bal = '0';
      }

      var addt_amount = fee_data[k].addt_amount;
      if (fee_data[k].addt_amount == null || fee_data[k].addt_amount == undefined ) {
        addt_amount = '0';
      }

      html += '<td>'+previous_bal+'</td>';
      html += '<td>'+addt_amount+'</td>';

      html += '<td>'+in_currency(fee_data[k].total_fee)+'</td>';
      html += '<td>'+in_currency(fee_data[k].total_fee_paid - fee_data[k].loan_provider_charges - fee_data[k].discount)+'</td>';
      html += '<td> ( '+in_currency(fee_data[k].total_concession)+' ) </td>';
      var colspan = '8';
      if (adjustment == 1) {
        let adj = fee_data[k].total_adjustment;
        if (adj === undefined || adj === null || adj === '') {
          html += '<td>-</td>';
        } else {
          html += '<td> ( '+in_currency(adj)+' ) </td>';
        }
        colspan = '9';
      }
      if (fineAmount) {
        html += '<td> '+in_currency(fee_data[k].total_fine)+' </td>';
        colspan = '9';
      }
      if (fineAmount && adjustment) {
         colspan = '10';
      }
      
      if (loan_column !=0 ) {
        html += '<td>'+in_currency(fee_data[k].loan_provider_charges)+'</td>';
        colspan = '9';
      }
      html += '<td>'+in_currency(fee_data[k].discount)+'</td>';
      html += '<td>'+in_currency(fee_data[k].total_balance)+'</td>';

      const feeAmount = parseFloat(fee_data[k].total_fee);
      const balance = parseFloat(fee_data[k].total_balance);
      let percentage = 0;
      if (feeAmount > 0) {
          percentage = (balance / feeAmount) * 100;
      }
      html += '<td>'+percentage.toFixed(2) + "%"+'</td>';
      html += '<td>'+in_currency(fee_data[k].total_due_amount)+'</td>';
      var std_bp = fee_data[k].bpId;

      for(var hbpId in headers){
        var insHeader = headers[hbpId];
        for(var hinsId in insHeader) {
          if(hbpId in std_bp && hinsId in std_bp[hbpId]) {   
           var due_amount = parseInt(std_bp[hbpId][hinsId].installment_amount) - parseInt(std_bp[hbpId][hinsId].installment_amount_paid) - parseInt(std_bp[hbpId][hinsId].concession);
            html += '<td style="border-left:2px solid #000" >'+in_currency(std_bp[hbpId][hinsId].installment_amount)+'</td>';
            html += '<td>'+in_currency(std_bp[hbpId][hinsId].installment_amount_paid)+'</td>';
            html += '<td> ( '+in_currency(std_bp[hbpId][hinsId].concession)+' )</td>';
            if (adjustment == 1) {
              if (std_bp[hbpId][hinsId] && std_bp[hbpId][hinsId].adjustment !== undefined) {
                html += '<td>( '+in_currency(std_bp[hbpId][hinsId].adjustment)+' ) </td>';
              } else {
                html += '<td>-</td>';
              }
            }
            if(fineAmount){
               html += '<td> '+in_currency(std_bp[hbpId][hinsId].fine)+' </td>';
            }
            html += '<td>'+in_currency(due_amount)+'</td>';
            html += '<td>'+status_arr[std_bp[hbpId][hinsId].status]+'</td>';
          } else {
            html += '<td>-</td>';
            html += '<td>-</td>';
            html += '<td>-</td>';
            if (adjustment == 1) {
              html += '<td>-</td>';
            }
            if(fineAmount == 1){
               html += '<td>-</td>';
            }
            html += '<td>-</td>';
            html += '<td>-</td>';
          }
        }

        // Add component data display if available
        if (has_component_data && fee_data[k].component_breakdown) {
          var componentBreakdown = fee_data[k].component_breakdown;
          var componentCount = Object.keys(component_headers).length;

          if (componentCount > 0) {
            html += '<td colspan="' + componentCount + '" style="border-left:2px solid #007bff; background-color: #f8f9fa; padding: 5px;">';
            html += '<div style="font-size: 11px; font-weight: bold; color: #007bff; margin-bottom: 3px;">Component Breakdown:</div>';

            for (var componentKey in component_headers) {
              if (componentBreakdown[componentKey]) {
                var comp = componentBreakdown[componentKey];
                html += '<div style="margin: 2px 0; font-size: 10px;">';
                html += '<strong>' + componentKey + ':</strong> ';
                html += 'Amt: ' + in_currency(comp.component_amount || 0) + ', ';
                html += 'Paid: ' + in_currency(comp.component_amount_paid || 0) + ', ';
                html += 'Bal: ' + in_currency(comp.balance_amount || 0);
                html += '</div>';
              } else {
                html += '<div style="margin: 2px 0; font-size: 10px; color: #999;">';
                html += '<strong>' + componentKey + ':</strong> No data';
                html += '</div>';
              }
            }
            html += '</td>';
          }
        }

        if (!transaction_hide_show) {
          if ((transactions).hasOwnProperty(fee_data[k].stdId)) {
            var trans = transactions[fee_data[k].stdId].transaction;
              html += '<td colspan="'+colspan+'" style="padding:0">';
              html +='<table id="trans_table" style="width: 100%;">';
              for(t in trans[hbpId]){
                html +='<tr>';
                html +='<td style="width: 10%;">'+trans[hbpId][t].paid_date+'</td>';
                html +='<td style="width: 11%;">'+trans[hbpId][t].receipt_number+'</td>';
                html +='<td style="width: 11%;">'+in_currency(trans[hbpId][t].amount_paid)+'</td>';
                html +='<td style="width: 11%;"> ( '+in_currency(trans[hbpId][t].concession_amount)+' ) </td>';
                if (adjustment) {
                  html +='<td style="width: 11%;"> ( '+in_currency(trans[hbpId][t].adjustment_amount)+' ) </td>';
                }
                if(fineAmount){
                  html +='<td style="width: 11%;">  '+in_currency(trans[hbpId][t].fine_amount)+' </td>';
                }

                html +='<td style="width: 14%;">'+paymenttypeAction(trans[hbpId][t].payment_type, trans[hbpId][t].reconciliation_status)+'</td>';
                html +='<td style="width: 11%;">'+trans[hbpId][t].bank_name+'</td>';
                if (trans[hbpId][t].payment_type == 1 || trans[hbpId][t].payment_type == 4 || trans[hbpId][t].payment_type == 8) {
                  html +='<td style="width: 11%;">'+trans[hbpId][t].cheque_or_dd_date+'</td>';
                }else{
                  html +='<td style="width: 11%;"></td>';
                }
                html +='<td style="width: 21%;">'+trans[hbpId][t].cheque_dd_nb_cc_dd_number+'</td>';
                html +='</tr>';
              }
              html +='</table>';
              html += '</td>'; 

          }else{

            html += '<td colspan="'+colspan+'" style="font-size: 14px;font-weight: 600;color: #8f8484c7;text-align: center;">No transactions</td>';
          }
        }
        
      }
      html += '</tr>';
      m++;
    }
    if(transaction_hide_show){
      tbody.append(html);
    }
    if(!transaction_hide_show){
      html +='</tbody>';
      $('#fee_summary_data').append(html);
    }
    index++;
    callReportGetter(index);
  }

  /**
   * Construct component-wise header for dual display
   */
  function constructComponentWiseHeader(component_headers) {
    var h_output = '<div class="table-responsive" id="report_width_container">';
    h_output += '<table class="table table-bordered table-striped" id="fee_summary_data">';
    h_output += '<thead style="display: table-header-group;">';
    h_output += '<tr>';
    h_output += '<th rowspan="2">#</th>';
    h_output += '<th rowspan="2">Student Name</th>';
    h_output += '<th rowspan="2">Class</th>';
    h_output += '<th rowspan="2">Section</th>';
    h_output += '<th rowspan="2">Admission No</th>';
    h_output += '<th rowspan="2">Enrollment No</th>';
    h_output += '<th rowspan="2">Component Name</th>';
    h_output += '<th rowspan="2">Installment</th>';
    h_output += '<th colspan="5" style="text-align:center;">Component Details</th>';
    h_output += '</tr>';
    h_output += '<tr>';
    h_output += '<th>Amount</th>';
    h_output += '<th>Paid</th>';
    h_output += '<th>Concession</th>';
    h_output += '<th>Fine</th>';
    h_output += '<th>Balance</th>';
    h_output += '</tr>';
    h_output += '</thead>';
    h_output += '</table></div>';
    $('.table-responsive').html(h_output);
    add_scroller('report_width_container');
  }

  /**
   * Construct component-wise report from dual display data
   */
  function constructComponentWiseReport(component_wise_data, component_headers, index) {
    var srNo = index * 150;
    var html = '<tbody>';
    var m = 0;

    for (var i = 0; i < component_wise_data.length; i++) {
      var row = component_wise_data[i];
      var statusColor = row.admission_status != '2' ? '#d30c0c' : '';

      html += '<tr style="color:' + statusColor + '">';
      html += '<td>' + (m + 1 + srNo) + '</td>';
      html += '<td>' + (row.student_name || 'N/A') + '</td>';
      html += '<td>' + (row.className || 'N/A') + '</td>';
      html += '<td>' + (row.sectionName || 'N/A') + '</td>';
      html += '<td>' + (row.admission_no || 'N/A') + '</td>';
      html += '<td>' + (row.enrollment_number || 'N/A') + '</td>';
      html += '<td><strong>' + (row.component_name || 'N/A') + '</strong></td>';
      html += '<td>' + (row.installment_name || 'N/A') + '</td>';
      html += '<td>' + in_currency(row.component_amount || 0) + '</td>';
      html += '<td>' + in_currency(row.component_amount_paid || 0) + '</td>';
      html += '<td>' + in_currency(row.concession_amount || 0) + '</td>';
      html += '<td>' + in_currency(row.fine_amount || 0) + '</td>';
      html += '<td>' + in_currency(row.balance_amount || 0) + '</td>';
      html += '</tr>';
      m++;
    }

    html += '</tbody>';
    $('#fee_summary_data').append(html);
    index++;
    callReportGetter(index);
  }

function in_currency(amount) {
  var formatter = new Intl.NumberFormat('en-IN', {
    // style: 'currency',
    currency: 'INR',
  });
  return formatter.format(amount);
}

function paymenttypeAction(paymentType, recon) {
    var NC = '';          
    if (recon == 1) {
      NC = '<span style="color:red;"> <b> (N/C) </b><span>';
    }else if(recon == 2){
      NC = '<span> <b> (C) </b><span>';
    }
    switch (paymentType) {
      case '1':
        pValue = 'DD '+NC;
        break;  
      case '2':
        pValue = 'Credit Card '+NC;
        break;
      case '3':
        pValue = 'Debit Card '+NC;
        break;
      case '4':
        pValue = 'Cheque '+NC;
        break;
      case '5':
        pValue = 'Wallet Payment '+NC;
        break;
      case '6':
        pValue = 'Challan '+NC;
        break;
      case '7':
        pValue = 'Card (POS) '+NC;
        break;
      case '8':
        pValue = 'Net Banking '+NC;
        break;
      case '9':
        pValue = 'Cash';
        break;
      case '10':
        pValue = 'Online Payment';
        break;
      default:
        pValue = '';
        break;
    }
    return pValue;
}
</script>

<script type="text/javascript">
  $('#installment_search').on('change',function(){
    if ($('#installment_search').is(':checked')) {
      $('#blueprintSelect').show();
      $('#multiBlueprintSelect').hide();
      $('.multiselect').removeAttr('id')
    }else{
      $('#blueprintSelect').hide();
      $('#multiBlueprintSelect').show();
      $('#installmentType').hide();
      $('#installment').hide();
      $('#installment_type').val('');
      $('#installmentId').val('');
      $('.multiselect').attr('id','fee_type');
    }
  });
</script>


<script type="text/javascript">
  
$('.changeFeeType').on('change',function(){
  var bpType =  $('.changeFeeType').val();
  $('#installment').hide();
  $('#installment_type').val('');
  $('#installmentType').hide();
  if (bpType != 'all') {
    changeInstallment_type(bpType);
    $('#installmentType').show();
  }else{
    $('#installmentId').val('');
    $('#installmentType').hide();
  }
});

function changeInstallment_type(bpType){
  $.ajax({
    url:'<?php echo site_url('reports/student/student_report/get_bpTypewise_insType') ?>',
    type:'post',
    data : {'bpType':bpType},
    success : function(data){
      var data = JSON.parse(data);
      var output = '<option value="">Select Installments Type</option>';
      for(var i=0; i < data.length; i++){
        output += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
      }
      $('#installment_type').html(output);
    }
  });
}
function changeInstallment() {
  $('#installment_type').on('change', function() {
    var installment_type = $(this).val();
    
    $('#installmentId').html('<option value="">Select Installments</option>'); 

    if (installment_type) {
      $.ajax({
        url: '<?php echo site_url('feesv2/reports/installment_type_installment') ?>',
        type: 'post',
        data: { 'installment_type': installment_type },
        success: function(data) {
          var data = JSON.parse(data);
          var output = '';  

          for (var i = 0; i < data.length; i++) {
            output += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
          }

          $('#installmentId').html(output);

          $('#installment').show();

          $('#installmentId').selectpicker('refresh');

          var allValues = [];
          $('#installmentId option').each(function() {
            allValues.push($(this).val()); 
          });

          $('#installmentId').selectpicker('val', allValues);

        },
        error: function(xhr, status, error) {
          console.error("Error fetching installments: ", error);
        }
      });
    } else {
      $('#installmentId').html('<option value="">Select Installments</option>');
      $('#installment').hide();
    }
  });
}


$(document).ready(function() {
  changeInstallment();
});

$('#installment_type').on('change',function(){
  changeInstallment();
}); 

$("#classId").change(function () {
    const previouslySelectedSections = $('#classSectionId').val();

    $('#classSectionId').html(''); 
    const selectedValue = $(this).val();

    $.ajax({
        url: '<?php echo site_url('feesv2/reports_v2/get_class_section_by_fees_selection_class') ?>',
        data: { 'feeclass': selectedValue },
        type: "post",
        success: function (data) {
            const resdata = JSON.parse(data);
            let option = '';
            resdata.forEach(item => {
                option += `<option value="${item.section_id}">${item.class_section}</option>`;
            });

            $("#classSectionId").html(option);
            $('#classSectionId').selectpicker('refresh');

            if (previouslySelectedSections) {
                $('#classSectionId').val(previouslySelectedSections).selectpicker('refresh');
            }
        },
        error: function (err) {
            console.log(err);
        }
    });
});
</script>
<style type="text/css">
  #progress{
    width: 100%;
    margin-top: 15px;
  }

  .table-responsive tr, .table-responsive td, .table-responsive th{
    max-width: 100%;
    white-space: nowrap;
  }
</style>

<script type="text/javascript">
  function printProfile(){
    var restorepage = document.body.innerHTML;
    var printcontent = document.getElementById('printArea').innerHTML;
    document.body.innerHTML = printcontent;
    window.print();
    document.body.innerHTML = restorepage;
  }

  function exportToExcel_daily(){
    var htmls = "";
    var uri = 'data:application/vnd.ms-excel;base64,';
    var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
    var base64 = function(s) {
        return window.btoa(unescape(encodeURIComponent(s)))
    };

    var format = function(s, c) {
        return s.replace(/{(\w+)}/g, function(m, p) {
            return c[p];
        })
    };

    var header = $("#print_visible").html();
    var mainTable = $(".fee_summary").html();
    var bodytable = $(".table-responsive").html();

    htmls ='<br><br>'+ header  + '<br><br>' + mainTable + '<br><br>'+bodytable;

    var ctx = {
      worksheet : 'Spreadsheet',
      table : htmls
    }

    var link = document.createElement("a");
    link.download = "export.xls";
    link.href = uri + base64(format(template, ctx));
    link.click();

  }

  function check_distrubance_details(){
    $('#distrubance_payment_details').modal('show');
    $.ajax({
      url:'<?php echo site_url('feesv2/reports_v2/fees_distrubance_student_details') ?>',
      type:"post",
      success:function(data){
        var resdata = $.parseJSON(data);
        var non_continue_list = resdata.studentData;
        $('#content_fees_student').html(construct_distrubance_report(non_continue_list));
        $('#distrubaneTable').DataTable({
          ordering:false,
          paging : true,
          scrollY :'40vh',
          responsive: true,
          "language": {
            "search": "",
            "searchPlaceholder": "Enter Search..."
          },
          "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
              "pageLength": 10,
          dom: 'lBfrtip',
            buttons: [
              {
              extend: 'excelHtml5',
              text: 'Excel',
              filename: 'distrubance report',
              className: 'btn btn-info'
              },
              {
              extend: 'print',
              text: 'Print',
              filename: 'distrubance report',
              className: 'btn btn-info'
              }
            ]
        });

        console.log(resdata);
      }
    });
  }
  function construct_distrubance_report(resdata){
    var html = '';
    html+= '<table class="table table-bordered" id="distrubaneTable">';
    html+= '<thead>';
    html+= '<tr>';
    html+= '<th>#</th>';
    html+= '<th>Student Name</th>';
    html+= '<th>Class/Section</th>';
    html+= '<th>Admission No.</th>';
    html+= '<th>Balance Amount</th>';
    html+= '</tr>';
    html+= '</thead>';
    html+= '<tbody>';
    for (let k = 0; k < resdata.length; k++) {
      html+= '<tr>';
      html+= '<td>'+(k+1)+'</td>';
      html+= '<td>'+resdata[k].student_name+'</td>';
      html+= '<td>'+resdata[k].class_name+'</td>';
      html+= '<td>'+resdata[k].admission_no+'</td>';
      html+= '<td>'+resdata[k].balance+'</td>';
      html+= '</tr>';
    }
    html+= '</tbody>';
    html+= '</table>';
    return html;

  }

  // Component-wise report functions
  function get_component_wise_report(){
    var fee_type = $('#fee_type').val();
    var classId = $('#classId').val();
    var classSectionId = $('#classSectionId').val();
    var admission_status = $('#admission_status').val();
    var admission_type = $('#admission_type').val();
    var category = $('#category').val();
    var rte_nrteId = $('#rte_nrteId').val();
    var donorsId = $('#donorsId').val();
    var combination = $('#combination').val();
    var staff_kid = $('#staff_kid').val();

    $(".loading-icon").show();
    $('.table-responsive').html('');
    $('.fee_summary').html('');
    $('.total_student_summary').hide();
    $('#search').prop('disabled', true).val('Please wait...');

    $.ajax({
      url: '<?php echo site_url('feesv2/reports_v2/student_wise_component_getStudentsForSummary'); ?>',
      type: 'post',
      data: {
        'fee_type': fee_type,
        'clsId': classId,
        'classSectionId': classSectionId,
        'admission_status': admission_status,
        'admission_type': admission_type,
        'category': category,
        'rte_nrteId': rte_nrteId,
        'donorsId': donorsId,
        'combination': combination,
        'staff_kid': staff_kid
      },
      success: function(data) {
        var cohort_student_ids = JSON.parse(data);
        if(cohort_student_ids.length > 0){
          process_student_cohorts(cohort_student_ids, 0, []);
        } else {
          $('.table-responsive').html('<div class="alert alert-warning">No students found with the selected criteria.</div>');
          $(".loading-icon").hide();
          $('#search').prop('disabled', false).val('Get Report');
        }
      },
      error: function() {
        $('.table-responsive').html('<div class="alert alert-danger">Error occurred while fetching data.</div>');
        $(".loading-icon").hide();
        $('#search').prop('disabled', false).val('Get Report');
      }
    });
  }

  function process_student_cohorts(cohort_student_ids, index, all_data){
    if(index >= cohort_student_ids.length){
      construct_component_wise_table(all_data);
      $(".loading-icon").hide();
      $('.total_student_summary').show();
      $('#search').prop('disabled', false).val('Get Report');
      return;
    }

    var fee_type = $('#fee_type').val();
    var classId = $('#classId').val();
    var classSectionId = $('#classSectionId').val();
    var admission_status = $('#admission_status').val();
    var admission_type = $('#admission_type').val();
    var category = $('#category').val();
    var rte_nrteId = $('#rte_nrteId').val();
    var donorsId = $('#donorsId').val();
    var combination = $('#combination').val();
    var staff_kid = $('#staff_kid').val();

    $.ajax({
      url: '<?php echo site_url('feesv2/reports_v2/get_student_wise_component_getStudentsForSummary'); ?>',
      type: 'post',
      data: {
        'cohortstudentids': cohort_student_ids[index],
        'fee_type': fee_type,
        'clsId': classId,
        'classSectionId': classSectionId,
        'admission_status': admission_status,
        'admission_type': admission_type,
        'category': category,
        'rte_nrteId': rte_nrteId,
        'donorsId': donorsId,
        'combination': combination,
        'staff_kid': staff_kid
      },
      success: function(data) {
        var result = JSON.parse(data);
        all_data.push(result);
        process_student_cohorts(cohort_student_ids, index + 1, all_data);
      },
      error: function() {
        $('.table-responsive').html('<div class="alert alert-danger">Error occurred while processing data.</div>');
        $(".loading-icon").hide();
        $('#search').prop('disabled', false).val('Get Report');
      }
    });
  }

  function construct_component_wise_table(all_data){
    // Get settings from global variables or set defaults
    var adjustment = window.adjustment || 0;
    var fineAmount = window.fineAmount || 0;
    var combination_column = window.combination_column || 0;

    var merged_data = {
      student_data: {},
      component_headers: {},
      feeArray: {}
    };

    // Merge all cohort data
    for(var i = 0; i < all_data.length; i++){
      var cohort_data = all_data[i];

      // Merge student data
      for(var student_id in cohort_data.student_data){
        merged_data.student_data[student_id] = cohort_data.student_data[student_id];
      }

      // Merge component headers
      for(var header in cohort_data.component_headers){
        merged_data.component_headers[header] = cohort_data.component_headers[header];
      }

      // Merge fee array
      for(var student_id in cohort_data.feeArray){
        if(!merged_data.feeArray[student_id]){
          merged_data.feeArray[student_id] = {};
        }
        for(var component in cohort_data.feeArray[student_id]){
          merged_data.feeArray[student_id][component] = cohort_data.feeArray[student_id][component];
        }
      }
    }

    // Generate summary data
    generateComponentSummary(merged_data);

    var html = '<table class="table table-bordered table-striped" id="component_wise_table">';
    html += '<thead>';
    html += '<tr>';
    html += '<th rowspan="2" class="component-header">S.No</th>';
    html += '<th rowspan="2" class="component-header">Student Name</th>';
    html += '<th rowspan="2" class="component-header">Class</th>';
    html += '<th rowspan="2" class="component-header">Admission No</th>';
    html += '<th rowspan="2" class="component-header">Enrollment No</th>';
    html += '<th rowspan="2" class="component-header">Boarding Type</th>';
    html += '<th rowspan="2" class="component-header">Application No</th>';
    if(combination_column == 1){
      html += '<th rowspan="2" class="component-header">Combination</th>';
    }
    html += '<th rowspan="2" class="component-header">Parent Name</th>';
    html += '<th rowspan="2" class="component-header">Parent Number</th>';

    // Component headers
    var component_headers_array = Object.keys(merged_data.component_headers);
    for(var i = 0; i < component_headers_array.length; i++){
      var colSpan = 5; // Base columns: Fee Amount, Collected, Concession, Discount, Balance
      if (adjustment == 1) colSpan++;
      if (fineAmount) colSpan++;
      html += '<th colspan="' + colSpan + '" class="component-header">' + component_headers_array[i] + '</th>';
    }
    html += '</tr>';

    html += '<tr>';
    for(var i = 0; i < component_headers_array.length; i++){
      html += '<th class="component-subheader">Fee Amount</th>';
      html += '<th class="component-subheader">Collected</th>';
      html += '<th class="component-subheader">Concession</th>';
      if (adjustment == 1) {
        html += '<th class="component-subheader">Adjustment</th>';
      }
      html += '<th class="component-subheader">Discount</th>';
      html += '<th class="component-subheader">Balance</th>';
      if (fineAmount) {
        html += '<th class="component-subheader">Fine</th>';
      }
    }
    html += '</tr>';
    html += '</thead>';
    html += '<tbody>';

    var sno = 1;
    for(var student_id in merged_data.student_data){
      var student = merged_data.student_data[student_id];
      html += '<tr>';
      html += '<td>' + sno + '</td>';
      html += '<td>' + student.first_name + ' ' + (student.last_name || '') + '</td>';
      html += '<td>' + student.class + ' ' + (student.section || '') + '</td>';
      html += '<td>' + student.admission_no + '</td>';
      html += '<td>' + (student.enrollment_number || '-') + '</td>';
      html += '<td>' + (student.boarding_type || '-') + '</td>';
      html += '<td>' + (student.application_no || '-') + '</td>';
      if(combination_column == 1){
        html += '<td>' + (student.combination || '-') + '</td>';
      }
      html += '<td>' + (student.parent_name || '-') + '</td>';
      html += '<td>' + (student.mobile_no || '-') + '</td>';

      // Component data
      for(var i = 0; i < component_headers_array.length; i++){
        var component_key = component_headers_array[i];
        var fee_data = merged_data.feeArray[student_id] && merged_data.feeArray[student_id][component_key] ? merged_data.feeArray[student_id][component_key] : null;

        if(fee_data){
          var feeAmount = parseFloat(fee_data.component_amount || 0);
          var paidAmount = parseFloat(fee_data.component_amount_paid || 0);
          var concessionAmount = parseFloat(fee_data.concession_amount || 0);
          var adjustmentAmount = parseFloat(fee_data.adjustment_amount || 0);
          var discountAmount = parseFloat(fee_data.discount_amount || 0);
          var fineAmountValue = parseFloat(fee_data.fine_amount || 0);
          var balanceAmount = parseFloat(fee_data.balance_amount || 0);

          html += '<td>' + feeAmount.toFixed(2) + '</td>';
          html += '<td>' + paidAmount.toFixed(2) + '</td>';
          html += '<td>' + concessionAmount.toFixed(2) + '</td>';
          if (adjustment == 1) {
            html += '<td>' + adjustmentAmount.toFixed(2) + '</td>';
          }
          html += '<td>' + discountAmount.toFixed(2) + '</td>';
          html += '<td>' + balanceAmount.toFixed(2) + '</td>';
          if (fineAmount) {
            html += '<td>' + fineAmountValue.toFixed(2) + '</td>';
          }
        } else {
          var emptyCols = 5; // Base columns: Fee Amount, Collected, Concession, Discount, Balance
          if (adjustment == 1) emptyCols++;
          if (fineAmount) emptyCols++;

          var emptyData = '';
          for(var j = 0; j < emptyCols; j++) {
            emptyData += '<td>0.00</td>';
          }
          html += emptyData;
        }
      }

      html += '</tr>';
      sno++;
    }

    html += '</tbody>';
    html += '</table>';

    // Clear any existing DataTable
    if ($.fn.DataTable.isDataTable('#component_wise_table')) {
      $('#component_wise_table').DataTable().destroy();
    }

    $('.table-responsive').html(html);

    var component_headers_array = Object.keys(merged_data.component_headers);
    var colsPerComponent = 5; // Base columns per component: Fee Amount, Collected, Concession, Discount, Balance
    if (adjustment == 1) colsPerComponent++;
    if (fineAmount) colsPerComponent++;

    var basicCols = 8; // S.No, Student Name, Class, Admission No, Enrollment No, Boarding Type, Application No, Parent Name, Parent Number
    if(combination_column == 1) basicCols++; // Add combination column if enabled

    var colLen = component_headers_array.length * colsPerComponent + basicCols;

    let table = $('#component_wise_table').DataTable({
      ordering: false,
      paging: false,
      dom: 'Bfrtip',
      info: false,
      scrollX: true,
      scrollY: "500px",
      scrollCollapse: true,
      'columnDefs': [
        { orderable: false, targets: '_all' },
        { className: 'text-center', targets: [0] },
        { className: '', targets: '_all' }
      ],
      buttons: [
        {
          extend: 'print',
          text: '<button class="btn btn-info"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
          title: 'Component wise Fee Report',
          footer: true,
          exportOptions: {
            columns: ':visible',
          }
        },
        {
          extend: 'excelHtml5',
          text: '<button class="btn btn-info"><span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel</button>',
          title: 'Component wise Fee Report',
          filename: 'Student_Component_Wise_Fee_Report',
          exportOptions: {
            columns: ':visible'
          },
          customize: function (xlsx) {
            var sheet = xlsx.xl.worksheets['sheet1.xml'];
            var sheetData = sheet.getElementsByTagName('sheetData')[0];

            // Get summary data
            var summaryTable = $('.fee_summary table');
            var summaryRowCount = summaryTable.find('tr').length;

            // Create summary rows with proper formatting
            var summaryRows = '';
            var rowIndex = 1;

            summaryTable.find('tr').each(function() {
              summaryRows += '<row r="' + rowIndex + '">';
              var colIndex = 0;

              $(this).find('th, td').each(function() {
                var cellValue = $(this).text().replace(/[^\w\s\.\-\(\)₹,]/gi, '');
                var colLetter = getColumnLetter(colIndex);

                summaryRows += '<c r="' + colLetter + rowIndex + '" t="inlineStr">';
                summaryRows += '<is><t>' + cellValue + '</t></is>';
                summaryRows += '</c>';
                colIndex++;
              });

              summaryRows += '</row>';
              rowIndex++;
            });

            // Add empty separator row
            summaryRows += '<row r="' + (rowIndex + 1) + '"></row>';
            var dataStartRow = rowIndex + 2;

            // Create proper two-row header structure
            var component_headers_array = Object.keys(window.currentMergedData.component_headers);
            var headerRows = '';

            // First header row (with component names and colspan)
            headerRows += '<row r="' + dataStartRow + '">';
            // Basic columns with rowspan
            headerRows += '<c r="A' + dataStartRow + '" t="inlineStr"><is><t>S.No</t></is></c>';
            headerRows += '<c r="B' + dataStartRow + '" t="inlineStr"><is><t>Student Name</t></is></c>';
            headerRows += '<c r="C' + dataStartRow + '" t="inlineStr"><is><t>Class</t></is></c>';
            headerRows += '<c r="D' + dataStartRow + '" t="inlineStr"><is><t>Admission No</t></is></c>';
            headerRows += '<c r="E' + dataStartRow + '" t="inlineStr"><is><t>Enrollment No</t></is></c>';
            headerRows += '<c r="F' + dataStartRow + '" t="inlineStr"><is><t>Boarding Type</t></is></c>';
            headerRows += '<c r="G' + dataStartRow + '" t="inlineStr"><is><t>Application No</t></is></c>';
            var colIndex = 7;
            if(combination_column == 1){
              headerRows += '<c r="' + getColumnLetter(colIndex) + dataStartRow + '" t="inlineStr"><is><t>Combination</t></is></c>';
              colIndex++;
            }
            headerRows += '<c r="' + getColumnLetter(colIndex) + dataStartRow + '" t="inlineStr"><is><t>Parent Name</t></is></c>';
            headerRows += '<c r="' + getColumnLetter(colIndex + 1) + dataStartRow + '" t="inlineStr"><is><t>Parent Number</t></is></c>';
            colIndex += 2;

            // Component headers
            var colsPerComponent = 5; // Base columns per component: Fee Amount, Collected, Concession, Discount, Balance
            if (adjustment == 1) colsPerComponent++;
            if (fineAmount) colsPerComponent++;

            for (var i = 0; i < component_headers_array.length; i++) {
              var colLetter = getColumnLetter(colIndex);
              headerRows += '<c r="' + colLetter + dataStartRow + '" t="inlineStr"><is><t>' + component_headers_array[i] + '</t></is></c>';
              colIndex += colsPerComponent; // Skip columns for the merge
            }
            headerRows += '</row>';

            // Second header row (sub-headers)
            var secondRowIndex = dataStartRow + 1;
            headerRows += '<row r="' + secondRowIndex + '">';
            // Skip basic columns (they are merged from row above)
            var basicCols = 8; // S.No, Student Name, Class, Admission No, Enrollment No, Boarding Type, Application No, Parent Name, Parent Number
            if(combination_column == 1) basicCols++; // Add combination column if enabled

            colIndex = basicCols;
            for (var i = 0; i < component_headers_array.length; i++) {
              var subHeaders = ['Fee Amount', 'Collected', 'Concession'];
              if (adjustment == 1) subHeaders.push('Adjustment');
              subHeaders.push('Discount', 'Balance');
              if (fineAmount) subHeaders.push('Fine');

              for (var j = 0; j < subHeaders.length; j++) {
                var colLetter = getColumnLetter(colIndex + j);
                headerRows += '<c r="' + colLetter + secondRowIndex + '" t="inlineStr"><is><t>' + subHeaders[j] + '</t></is></c>';
              }
              colIndex += subHeaders.length;
            }
            headerRows += '</row>';

            // Get existing data (skip the original headers) and update row numbers
            var existingRows = sheetData.innerHTML;
            // Remove original header rows (first 2 rows)
            existingRows = existingRows.replace(/<row r="1"[^>]*>.*?<\/row>/g, '');
            existingRows = existingRows.replace(/<row r="2"[^>]*>.*?<\/row>/g, '');

            // Update remaining row numbers
            var bodyStartRow = secondRowIndex + 1;
            var updatedRows = existingRows.replace(/r="(\d+)"/g, function(match, rowNum) {
              var originalRowNum = parseInt(rowNum);
              if (originalRowNum > 2) { // Only update data rows, not headers
                var newRowNum = originalRowNum - 2 + bodyStartRow;
                return 'r="' + newRowNum + '"';
              }
              return match;
            });

            // Update cell references for data rows
            updatedRows = updatedRows.replace(/r="([A-Z]+)(\d+)"/g, function(match, col, rowNum) {
              var originalRowNum = parseInt(rowNum);
              if (originalRowNum > 2) {
                var newRowNum = originalRowNum - 2 + bodyStartRow;
                return 'r="' + col + newRowNum + '"';
              }
              return match;
            });

            // Combine all rows
            sheetData.innerHTML = summaryRows + headerRows + updatedRows;

            // Handle merged cells
            var mergeCells = sheet.getElementsByTagName('mergeCells')[0];
            if (!mergeCells) {
              mergeCells = sheet.createElement('mergeCells');
              sheet.getElementsByTagName('worksheet')[0].appendChild(mergeCells);
            }

            var mergeCount = 0;

            // Merge basic columns (rowspan for basic columns)
            var basicCols = 8; // S.No, Student Name, Class, Admission No, Enrollment No, Boarding Type, Application No, Parent Name, Parent Number
            if(combination_column == 1) basicCols++; // Add combination column if enabled

            for (var i = 0; i < basicCols; i++) {
              var colLetter = getColumnLetter(i);
              var mergeRange = colLetter + dataStartRow + ':' + colLetter + secondRowIndex;
              var mergeCell = sheet.createElement('mergeCell');
              mergeCell.setAttribute('ref', mergeRange);
              mergeCells.appendChild(mergeCell);
              mergeCount++;
            }

            // Merge component headers (colspan)
            var startCol = basicCols;
            for (var i = 0; i < component_headers_array.length; i++) {
              var startColLetter = getColumnLetter(startCol + (i * colsPerComponent));
              var endColLetter = getColumnLetter(startCol + (i * colsPerComponent) + colsPerComponent - 1);
              var mergeRange = startColLetter + dataStartRow + ':' + endColLetter + dataStartRow;

              var mergeCell = sheet.createElement('mergeCell');
              mergeCell.setAttribute('ref', mergeRange);
              mergeCells.appendChild(mergeCell);
              mergeCount++;
            }

            if (mergeCount > 0) {
              mergeCells.setAttribute('count', mergeCount.toString());
            }
          }
        },
      ],
    });

    // Add column visibility button
    initializeColVisButton(table, 'component_wise_table_wrapper', colLen);

    // Store merged_data globally for Excel export
    window.currentMergedData = merged_data;
  }

  function generateComponentSummary(merged_data) {
    var component_headers_array = Object.keys(merged_data.component_headers);
    var summaryData = {};
    var totalStudents = Object.keys(merged_data.student_data).length;

    // Initialize summary data for each component
    for(var i = 0; i < component_headers_array.length; i++){
      var componentKey = component_headers_array[i];
      summaryData[componentKey] = {
        total_fee_amount: 0,
        total_fee_collected: 0,
        total_concession: 0,
        total_adjustment: 0,
        total_discount: 0,
        total_fine: 0,
        total_balance: 0,
        student_count: 0
      };
    }

    // Calculate totals for each component
    for(var student_id in merged_data.feeArray){
      for(var componentKey in merged_data.feeArray[student_id]){
        var feeData = merged_data.feeArray[student_id][componentKey];
        if(summaryData[componentKey]){
          summaryData[componentKey].total_fee_amount += parseFloat(feeData.component_amount || 0);
          summaryData[componentKey].total_fee_collected += parseFloat(feeData.component_amount_paid || 0);
          summaryData[componentKey].total_concession += parseFloat(feeData.concession_amount || 0);
          summaryData[componentKey].total_adjustment += parseFloat(feeData.adjustment_amount || 0);
          summaryData[componentKey].total_discount += parseFloat(feeData.discount_amount || 0);
          summaryData[componentKey].total_fine += parseFloat(feeData.fine_amount || 0);
          summaryData[componentKey].total_balance += parseFloat(feeData.balance_amount || 0);
          summaryData[componentKey].student_count++;
        }
      }
    }

    // Generate summary HTML
    var fee_summary = '<table class="table table-bordered" style="width: 100%; margin-bottom: 20px;">';
    fee_summary += '<thead>';
    fee_summary += '<tr>';
    fee_summary += '<th>Component</th>';
    fee_summary += '<th># Students</th>';
    fee_summary += '<th>Fee Amount</th>';
    fee_summary += '<th>Collected Amount</th>';
    fee_summary += '<th>Concession</th>';
    if (adjustment == 1) {
      fee_summary += '<th>Adjustment</th>';
    }
    fee_summary += '<th>Discount</th>';
    fee_summary += '<th>Balance</th>';
    if (fineAmount) {
      fee_summary += '<th>Fine</th>';
    }
    fee_summary += '</tr>';
    fee_summary += '</thead>';
    fee_summary += '<tbody>';

    var gtotal_fee_amount = 0;
    var gtotal_fee_collected = 0;
    var gtotal_concession = 0;
    var gtotal_adjustment = 0;
    var gtotal_discount = 0;
    var gtotal_fine = 0;
    var gtotal_balance = 0;

    for(var componentKey in summaryData) {
      var data = summaryData[componentKey];
      gtotal_fee_amount += data.total_fee_amount;
      gtotal_fee_collected += data.total_fee_collected;
      gtotal_concession += data.total_concession;
      gtotal_adjustment += data.total_adjustment;
      gtotal_discount += data.total_discount;
      gtotal_fine += data.total_fine;
      gtotal_balance += data.total_balance;

      fee_summary += '<tr>';
      fee_summary += '<th style="font-size:14px; color:#7ea3d2"><b>' + componentKey + '</b></th>';
      fee_summary += '<td>' + totalStudents + '</td>';
      fee_summary += '<td>' + in_currency_component(data.total_fee_amount) + '</td>';
      fee_summary += '<td>' + in_currency_component(data.total_fee_collected) + '</td>';
      fee_summary += '<td>' + in_currency_component(data.total_concession) + '</td>';
      if (adjustment == 1) {
        fee_summary += '<td>' + in_currency_component(data.total_adjustment) + '</td>';
      }
      fee_summary += '<td>' + in_currency_component(data.total_discount) + '</td>';
      fee_summary += '<td>' + in_currency_component(data.total_balance) + '</td>';
      if (fineAmount) {
        fee_summary += '<td>' + in_currency_component(data.total_fine) + '</td>';
      }
      fee_summary += '</tr>';
    }

    // Grand total row
    fee_summary += '<tr style="background-color: #f8f9fa; font-weight: bold;">';
    fee_summary += '<th>Grand Total</th>';
    fee_summary += '<th>' + totalStudents + '</th>';
    fee_summary += '<th>' + in_currency_component(gtotal_fee_amount) + '</th>';
    fee_summary += '<th>' + in_currency_component(gtotal_fee_collected) + '</th>';
    fee_summary += '<th>' + in_currency_component(gtotal_concession) + '</th>';
    if (adjustment == 1) {
      fee_summary += '<th>' + in_currency_component(gtotal_adjustment) + '</th>';
    }
    fee_summary += '<th>' + in_currency_component(gtotal_discount) + '</th>';
    fee_summary += '<th>' + in_currency_component(gtotal_balance) + '</th>';
    if (fineAmount) {
      fee_summary += '<th>' + in_currency_component(gtotal_fine) + '</th>';
    }
    fee_summary += '</tr>';

    fee_summary += '</tbody>';
    fee_summary += '</table>';

    $('.fee_summary').html(fee_summary);
  }

  function in_currency_component(amount) {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }

  function getColumnLetter(columnIndex) {
    var letter = '';
    while (columnIndex >= 0) {
      letter = String.fromCharCode(65 + (columnIndex % 26)) + letter;
      columnIndex = Math.floor(columnIndex / 26) - 1;
    }
    return letter;
  }
</script>


<div id="distrubance_payment_details" class="modal fade" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content" style="width:60%; margin:auto">
        <div class="modal-header">
        <h4 class="modal-title">Non-Continuing Student Balance Details</h4>
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body" style="overflow-y:auto;">
          <div id="modal-loader" style="display: none;"></div>
          <div id="content_fees_student">
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-danger" style="width: 9rem; border-radius: .45rem;" data-dismiss="modal">Close</button>
        </div>
    </div>
  </div>
</div>

<style>
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

  table {
    font-family: 'Poppins', sans-serif !important;
    margin-left: 20px !important;
  }

  b {
    font-weight: 500;
  }

  .dataTables_filter{
    border-bottom : none;
  }

  .buttons-print {
    padding: 2px !important;
  }

  .buttons-colvis {
    padding: 2px !important;
  }

  .buttons-excel {
    border: none !important;
    background: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .dt-button {
    border: none !important;
    background: none !important;
  }

  .btn-info {
    border-radius: 8px !important;
  }

  .dt-button .btn {
    line-height: 20px;
  }

  .dt-buttons {
    text-align: right;
    float: right;
  }

  .dataTables_scrollHeadInner table {
    margin: 0px;
  }

  .search-box {
    display: inline-block;
    position: relative;
    margin-right: 2px;
    vertical-align: middle;
  }

  .input-search {
    line-height: 1.5;
    padding: 5px 10px;
    display: inline;
    width: 177px;
    height: 27px;
    background-color: #f2f2f2 !important;
    border: 1px solid #ccc !important;
    border-radius: 4px !important;
    margin-right: 0 !important;
    font-size: 14px;
    color: #495057;
    outline: none;
    margin-bottom: 10px;
  }

  .input-search::placeholder {
    color: rgba(73, 80, 87, 0.5);
    font-size: 14px;
    font-weight: 300;
  }

  .panel-controls {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  #fee_summary_data,
  #distrubaneTable {
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
    border-radius: 1.5rem;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    opacity: 1 !important;
    transition: none !important;
  }

  .table-responsive {
    margin-top: 20px;
  }

  .fee_summary {
    margin-top: 20px;
  }

  #fee_summary_data thead th,
  #distrubaneTable thead th {
    position: sticky !important;
    top: 0;
    background-color: #f1f5f9;
    color: #111827;
    font-size: 13px;
    font-weight: 500;
    z-index: 10;
    text-align: left;
    padding: 12px 16px;
  }

  #fee_summary_data th,
  #fee_summary_data td,
  #distrubaneTable th,
  #distrubaneTable td {
    padding: 10px 14px;
    border-bottom: 1px solid #e5e7eb;
    font-size: 12px;
    font-weight: 400;
  }

  #fee_summary_data tbody tr:nth-child(even) {
    background-color: #f9fafb;
  }

  #fee_summary_data tbody tr:hover,
  #distrubaneTable tbody tr:hover {
    background-color: #f1f5f9;
  }

  #fee_summary_data tfoot tr,
  #distrubaneTable tfoot tr {
    background-color: #f3f4f6;
    font-weight: 500;
  }

  .dt-button-collection .buttons-columnVisibility:before {
    content: ' ';
    margin-top: -6px;
    margin-left: 10px;
    border: 1px solid black;
    border-radius: 3px;
  }

  .dt-button-collection .buttons-columnVisibility:before,
  .dt-button-collection .buttons-columnVisibility.active span:before {
    display: block;
    position: absolute;
    top: 1.2em;
    left: 0;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
  }

  .dt-button-collection .buttons-columnVisibility span {
    margin-left: 20px;
  }

  .dt-button-collection .buttons-columnVisibility.active span:before {
    content: '\2714';
    /* Unicode checkmark character */
    margin-top: -13px;
    margin-left: 12px;
    text-align: center;
    text-shadow: 1px 1px #DDD, -1px -1px #DDD, 1px -1px #DDD, -1px 1px #DDD;
  }

  div.dt-button-collection .dt-button {
    position: relative;
    left: 0;
    right: 0;
    width: 100%;
    display: block;
    float: none;
    background: none;
    margin: 0;
    padding: .5em 1em;
    border: none;
    text-align: left;
    cursor: pointer;
    color: inherit;
  }

  div.dt-button-collection {
    position: absolute;
    top: 0;
    left: 0;
    width: 200px;
    margin-top: 3px;
    margin-bottom: 3px;
    padding: .75em 0;
    border: 1px solid rgba(0, 0, 0, 0.4);
    background-color: white;
    overflow: hidden;
    z-index: 2002;
    border-radius: 5px;
    box-shadow: 3px 4px 10px 1px rgba(0, 0, 0, 0.3);
    box-sizing: border-box;
  }

  div.dt-button-collection .dt-button {
    padding: 1px 1rem;
  }

  /* Component-wise table styles */
  .component-header {
    background-color: #f8f9fa;
    font-weight: bold;
    text-align: center;
  }

  .component-subheader {
    background-color: #e9ecef;
    font-weight: 600;
    text-align: center;
    font-size: 12px;
  }

  #component_wise_table thead th {
    position: sticky !important;
    top: 0;
    background-color: #f1f5f9;
    color: #111827;
    font-weight: 600;
    border: 1px solid #d1d5db;
    padding: 12px 8px;
    text-align: center;
    vertical-align: middle;
    z-index: 10;
  }

  #component_wise_table tbody td {
    border: 1px solid #d1d5db;
    padding: 8px;
    vertical-align: middle;
  }
</style>

<script>
$(document).ready(function() {
  // Handle component-wise toggle
  $('#show_component_wise').change(function() {
    if ($(this).is(':checked')) {
      // Clear existing data and refresh with component view
      $('.table-responsive').html('<div class="alert alert-info">Please click "Get Report" to view component-wise data.</div>');
    } else {
      // Clear existing data and refresh with regular view
      $('.table-responsive').html('<div class="alert alert-info">Please click "Get Report" to view regular installment data.</div>');
    }
  });

  // Add visual indicator for dual display mode
  $('#show_component_wise').parent().append('<small style="color: #666; display: block; margin-top: 5px;">Toggle between regular installment view and component-wise view</small>');
});
</script>